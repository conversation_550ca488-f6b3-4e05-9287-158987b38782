﻿<template>
	<view class="hc-ord-product-edit-item" v-if="item" :key="index">
		<view class="hc-ord-product-edit-item-left"><hc-image :src="$hc.Command.GetAttachmentURL(item.FATTACHMENT_ID)"></hc-image></view>
		<view style="flex: 1;">
			<hc-row>
				<hc-col :span="7">
					<view class="hc-ord-product-edit-item-content">
						<view class="hc-ord-product-edit-item-content-title u-line-2">{{ item.FPRODUCT_NAME }}</view>
						<view class="hc-ord-product-edit-item-content-type">品号:{{ item.FPRODUCT_CODE }}</view>
					</view>
				</hc-col>
				<hc-col :span="5">
					<view class="hc-ord-product-edit-item-right">
						<hc-field icon="arrow-down-fill" v-model="item.FPRICE"></hc-field> 
						<hc-field icon="arrow-down-fill" v-model="item.FQTY"></hc-field> 
						<hc-field icon="arrow-down-fill" v-model="item.FRATE"></hc-field>
					</view>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="12">
					<view class="hc-ord-product-edit-item-content">
						<view class="hc-ord-product-edit-item-content-type pro-desc">规格:{{ item.FPRODUCT_DESC }}</view>
					</view>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
/*
 * @example <hc-ord-product-edit item="{}" index="0"></hc-ord-product-edit>
 */
export default {
	name: 'hc-ord-product-edit',
	props: {
		item: {
			type: Object,
			default() {
				return {};
			}
		},
		index: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {};
	}
};
</script>

<style lang="scss">
.hc-ord-product-edit-item {
	width: 100%;
	display: flex;
	padding: 20rpx 12rpx;
	border-top: 1rpx solid #eef1f5;

	&-left {
		margin-right: 20rpx;
		width: 160rpx;
		height: 160rpx;
	}

	&-content {
		&-title {
			font-size: 28rpx;
			line-height: 50rpx;
		}

		&-type {
			margin: 14rpx 0;
			font-size: 26rpx;
			color: $u-tips-color;
		}

		&-delivery-time {
			color: #000000;
			font-size: 24rpx;
		}
	}

	&-right {
		padding-right: 10rpx;
		text-align: right;
		&-decimal {
			font-size: 24rpx;
			margin-top: 4rpx;
		}
	}
}
.hc-ord-product-edit-pro-desc {
	width: 450rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
</style>
