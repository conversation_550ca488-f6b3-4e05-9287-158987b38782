<template>
	<view class="hc-pcd">
		<hc-popup :maskCloseAble="maskCloseAble" mode="bottom" :popup="false" v-model="value" length="auto"
			:safeAreaInsetBottom="safeAreaInsetBottom" @close="close" :z-index="uZIndex">
			<view class="hc-pcd">
				<view class="hc-pcd__header" @touchmove.stop.prevent="stop" catchtouchmove="stop">
					<view class="hc-pcd__header__cancel hc-pcd__header__btn" :style="{ color: cancelColor }"
						hover-class="u-hover-class" :hover-stay-time="150" @tap="getResult('cancel')">
						取消
					</view>
					<view class="hc-pcd__header__confirm hc-pcd__header__btn" :style="{ color: $themeCurrent.main }"
						hover-class="u-hover-class" :hover-stay-time="150" @touchmove.stop=""
						@tap.stop="getResult('confirm')">
						确定
					</view>
				</view>
				<view class="hc-pcd__body">
					<picker-view @change="columnChange" class="hc-pcd__body__picker-view" :value="selectedIndexs">
						<picker-view-column>
							<view class="hc-pcd__body__picker-view__item" v-for="(item, index) in realNation"
								:key="index">
								<view class="u-line-1">{{ item.FNATION_NAME }}</view>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="hc-pcd__body__picker-view__item" v-for="(item, index) in provinceList"
								:key="index">
								<view class="u-line-1">{{ item.FPROVINCE_NAME }}</view>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="hc-pcd__body__picker-view__item" v-for="(item, index) in cityList"
								:key="index">
								<view class="u-line-1">{{ item.FCITY_NAME }}</view>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="hc-pcd__body__picker-view__item" v-for="(item, index) in districtList"
								:key="index">
								<view class="u-line-1">{{ item.FDISTRICT_NAME }}</view>
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</hc-popup>
	</view>
</template>

<script>
	export default {
		name: "hc-pcd",
		props: {
			// 通过双向绑定控制组件的弹出与收起
			value: {
				type: Boolean,
				default: false
			},
			// "取消"按钮的颜色
			cancelColor: {
				type: String,
				default: '#606266'
			},
			// "确定"按钮的颜色
			// confirmColor: {
			// 	type: String,
			// 	default: '#2979ff'
			// },
			// 弹出的z-index值
			zIndex: {
				type: [String, Number],
				default: 0
			},
			safeAreaInsetBottom: {
				type: Boolean,
				default: false
			},
			// 是否允许通过点击遮罩关闭Picker
			maskCloseAble: {
				type: Boolean,
				default: true
			},
			// 提供的默认选中的id
			defaultValue: {
				type: Array,
				default () {
					return [];
				}
			},
			// 是否平台端
			isPlatform: {
				type: Boolean,
				default: false
			},
		},
		data() {
			return {
				selectedIndexs: [0, 0, 0,0],
				provinceList: [],
				cityList: [],
				districtList: [],
			};
		},
		watch: {
			isPlatform: {
				handler(newVal) {
					if (newVal) {
						if (!this.$store.state.nation.length) {
							this.$store.dispatch("getNation");
						} else {
							const [nationID, provinceID, cityID, districtID] = this.defaultValue;
							let nationIndex = this.$store.state.nation.findIndex(item => item.FDIVISION_ID === nationID);
							nationIndex = nationIndex < 0 ? 0 : nationIndex;
							this.selectedIndexs[0] = nationIndex;
							this.getInitCD(this.$store.state.nation[nationIndex], provinceID, cityID, districtID);
						}
					} else {
						if (!this.$store.state.entNation.length) {
							this.$store.dispatch("getEntNation");
						} else {
							const [nationID, provinceID, cityID, districtID] = this.defaultValue;
							let nationIndex = this.$store.state.entNation.findIndex(item => item.FDIVISION_ID ===
							nationID);
							nationIndex = nationIndex < 0 ? 0 : nationIndex;
							this.selectedIndexs[0] = nationIndex;
							this.getInitCD(this.$store.state.entNation[nationIndex], provinceID, cityID, districtID);
						}
					}
				},
				immediate: true
			},
			'$store.state.nation': function(newVal) {
				if (this.isPlatform) {
					const [nationID, provinceID, cityID, districtID] = this.defaultValue;
					let nationIndex = newVal.findIndex(item => item.FDIVISION_ID === nationID);
					nationIndex = nationIndex < 0 ? 0 : nationIndex;
					this.selectedIndexs[0] = nationIndex;
					this.getInitCD(newVal[nationIndex], provinceID, cityID, districtID);
				}
			},
			'$store.state.entNation': function(newVal) {
				if (!this.isPlatform) {
					const [nationID, provinceID, cityID, districtID] = this.defaultValue;
					let nationIndex = newVal.findIndex(item => item.FDIVISION_ID === nationID);
					nationIndex = nationIndex < 0 ? 0 : nationIndex;
					this.selectedIndexs[0] = nationIndex;
					this.getInitCD(newVal[nationIndex], provinceID, cityID, districtID);
				}
			},
			defaultValue(newVal) {
				if (this.realNation.length) {
					const [nationID, provinceID, cityID, districtID] = newVal;
					let nationIndex = this.realNation.findIndex(item => item.FDIVISION_ID === nationID);
					nationIndex = nationIndex < 0 ? 0 : nationIndex;
					this.selectedIndexs[0] = nationIndex;
					this.getInitCD(this.realNation[nationIndex], provinceID, cityID, districtID);
				}
			}
		},
		computed: {
			uZIndex() {
				// 如果用户有传递z-index值，优先使用
				return this.zIndex ? this.zIndex : this.$hc.zIndex.popup;
			},
			realNation() {
				return this.isPlatform ? this.$store.state.nation : this.$store.state.entNation;
			}
		},
		methods: {
			close() {
				this.$emit('input', false);
			},
			// 点击确定或者取消
			getResult(event = null) {
				const [nationIndex, provinceIndex, cityIndex, districtIndex] = this.selectedIndexs;
				const result = [this.realNation[nationIndex], this.provinceList[provinceIndex], this.cityList[cityIndex],
					this.districtList[districtIndex]
				]
				if (event) this.$emit(event, result);
				this.close();
			},
			async getInitCD(nation, provinceID, cityID, districtID) {
				//省
				const provinceList = await this.$hc.request({
					url: this.isPlatform ? this.$hc.api.CPF029DivisionGetProviceByNationCodeAsync : this.$hc
						.api.DivisionGetProviceByNationCodeAsync,
					disableLoading: true,
					data: {
						nationCode: nation.FNATION_CODE,
					}
				}).then(res => res.Entity || [])
				if (provinceList.length) {
					this.provinceList = provinceList;
					let provinceIndex = provinceList.findIndex(item => item.FDIVISION_ID === provinceID);
					provinceIndex = provinceIndex < 0 ? 0 : provinceIndex;
					this.selectedIndexs[1] = provinceIndex;

					//市
					const cityList = await this.$hc.request({
						url: this.isPlatform ? this.$hc.api.CPF029DivisionGetCityByProviceCodeAsync : this.$hc
							.api.DivisionGetCityByProviceCodeAsync,
						disableLoading: true,
						data: {
							proviceCode: provinceList[provinceIndex].FPROVINCE_CODE,
						}
					}).then(res => res.Entity || [])

					if (cityList.length) {
						this.cityList = cityList;
						let cityIndex = cityList.findIndex(item => item.FDIVISION_ID === cityID);
						cityIndex = cityIndex < 0 ? 0 : cityIndex;
						this.selectedIndexs[2] = cityIndex;

						//区县
						const districtList = await this.$hc.request({
							url: this.isPlatform ? this.$hc.api.CPF029DivisionGetDistrictByCityCodeAsync : this
								.$hc.api.DivisionGetDistrictByCityCodeAsync,
							disableLoading: true,
							data: {
								cityCode: cityList[cityIndex].FCITY_CODE,
							}
						}).then(res => res.Entity || [])
						if (districtList.length) {
							this.districtList = districtList;
							let districtIndex = districtList.findIndex(item => item.FDIVISION_ID === districtID);
							districtIndex = districtIndex < 0 ? 0 : districtIndex;
							this.selectedIndexs[3] = districtIndex;
						}
					}
				}


			},
			async columnChange(e) {
				const [nationIndex,provinceIndex, cityIndex, districtIndex] = e.detail.value;
				if (nationIndex !== this.selectedIndexs[0]) {
					this.selectedIndexs.splice(0, 1, nationIndex)
					const provinceList = await this.$hc.request({
						url: this.isPlatform ? this.$hc.api.CPF029DivisionGetCityByProviceCodeAsync : this.$hc
							.api.DivisionGetCityByProviceCodeAsync,
						disableLoading: true,
						data: {
							nationCode: this.realNation[nationIndex].FNATION_CODE,
						}
					}).then(res => res.Entity || [])
					this.provinceList = provinceList;
					
					if (provinceList.length) {											
						const cityList = await this.$hc.request({
							url: this.isPlatform ? this.$hc.api.CPF029DivisionGetCityByProvinceCodeAsync : this
								.$hc.api.DivisionGetCityByProviceCodeAsync,
							disableLoading: true,
							data: {
								provinceCode: provinceList[0].FPROVINCE_CODE,
							}
						}).then(res => res.Entity || [])
						this.selectedIndexs.splice(1, 1, 0);
						
						this.cityList = cityList;
						if(cityList.length){							
							const districtList = await this.$hc.request({
								url: this.isPlatform ? this.$hc.api.CPF029DivisionGetDistrictByCityCodeAsync : this
									.$hc.api.DivisionGetDistrictByCityCodeAsync,
								disableLoading: true,
								data: {
									cityCode: cityList[0].FCITY_CODE,
								}
							}).then(res => res.Entity || [])
							this.selectedIndexs.splice(2, 1, 0);
							
							this.districtList = districtList;
							this.selectedIndexs.splice(3, 1, 0);							
						}						
					}
				} else if (provinceIndex !== this.selectedIndexs[1]) {
					this.selectedIndexs.splice(1, 1, provinceIndex)
					const cityList = await this.$hc.request({
						url: this.isPlatform ? this.$hc.api.CPF029DivisionGetCityByProviceCodeAsync : this.$hc
							.api.DivisionGetCityByProviceCodeAsync,
						disableLoading: true,
						data: {
							proviceCode: this.provinceList[provinceIndex].FPROVINCE_CODE,
						}
					}).then(res => res.Entity || [])
					this.cityList = cityList;
					if (cityList.length) {						
						const districtList = await this.$hc.request({
							url: this.isPlatform ? this.$hc.api.CPF029DivisionGetDistrictByCityCodeAsync : this
								.$hc.api.DivisionGetDistrictByCityCodeAsync,
							disableLoading: true,
							data: {
								cityCode: cityList[0].FCITY_CODE,
							}
						}).then(res => res.Entity || [])
						this.selectedIndexs.splice(2, 1, 0);
						this.districtList = districtList;
						if (districtList.length) {							
							this.selectedIndexs.splice(3, 1, 0);
						}
					}
				} 
				else if (cityIndex !== this.selectedIndexs[2]) {
					this.selectedIndexs.splice(2, 1, cityIndex);
					const districtList = await this.$hc.request({
						url: this.isPlatform ? this.$hc.api.CPF029DivisionGetDistrictByCityCodeAsync : this.$hc
							.api.DivisionGetDistrictByCityCodeAsync,
						disableLoading: true,
						data: {
							cityCode: this.cityList[cityIndex].FCITY_CODE,
						}
					}).then(res => res.Entity || [])
					this.selectedIndexs.splice(3, 1, 0);
					this.districtList = districtList;
				} else {
					this.selectedIndexs.splice(3, 1, districtIndex);
				}
			}
		}
	}
</script>

<style lang="scss">
	.hc-pcd {
		&__action {
			position: relative;
			line-height: $u-form-item-height;
			height: $u-form-item-height;

			&__icon {
				position: absolute;
				right: 20rpx;
				top: 50%;
				transition: transform .4s;
				transform: translateY(-50%);
				z-index: 1;

				&--reverse {
					transform: rotate(-180deg) translateY(50%);
				}
			}
		}

		&--border {
			border-radius: 6rpx;
			border-radius: 4px;
			border: 1px solid $u-form-item-border-color;
		}

		&__header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 80rpx;
			padding: 0 40rpx;
		}

		&__body {
			width: 100%;
			height: 500rpx;
			overflow: hidden;
			background-color: #fff;

			&__picker-view {
				height: 100%;
				box-sizing: border-box;

				&__item {
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 32rpx;
					color: $u-main-color;
					padding: 0 8rpx;
				}
			}
		}
	}
</style>
