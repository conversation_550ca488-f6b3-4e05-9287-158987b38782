export default {
	/**
	 * 注册服务
	 * @param {Object} callback
	 * @param {Object} action  //服务名称
	 */
    listenScanStatus(receiver, action, callback) {
		var that = this;
		try {
			 // #ifdef APP-PLUS
			var main = plus.android.runtimeMainActivity();
			var context = plus.android.importClass('android.content.Context'); //上下文
			if (receiver == null) {
			
				receiver = plus.android.implements('io.dcloud.feature.internal.reflect.BroadcastReceiver', {
					onReceive: doReceive
				});
			}else {
				main.unregisterReceiver(receiver);
			}
			var IntentFilter = plus.android.importClass('android.content.IntentFilter');
			var Intent = plus.android.importClass('android.content.Intent');
			var filter = new IntentFilter();
			filter.addAction(action); //监听扫描
			main.registerReceiver(receiver, filter); //注册监听
			function doReceive(context, intent) {
				
				//通过intent实例引入intent类，方便以后的‘.’操作
				plus.android.importClass(intent);
				//条码内容
				var barcode = intent.getStringExtra('value');
				//条码长度
				// var barcodeLengt = intent.getIntExtra('length');

				callback(barcode);
			}
			 // #endif
		} catch (e) {
			console.error(e);
		}
	},
	byteToString(arr) {
		if (typeof arr === 'string') {
			return arr;
		}
		var str = '',
			_arr = arr;
		for (var i = 0; i < _arr.length; i++) {
			var one = _arr[i].toString(2),
				v = one.match(/^1+?(?=0)/);
			if (v && one.length == 8) {
				var bytesLength = v[0].length;
				var store = _arr[i].toString(2).slice(7 - bytesLength);
				for (var st = 1; st < bytesLength; st++) {
					store += _arr[st + i].toString(2).slice(2);
				}
				str += String.fromCharCode(parseInt(store, 2));
				i += bytesLength - 1;
			} else {
				str += String.fromCharCode(_arr[i]);
			}
		}
		return str;
	}
}
