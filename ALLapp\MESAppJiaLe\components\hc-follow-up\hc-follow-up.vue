﻿<template>
	<view class="item" v-if="item" @tap="Tap">
		<hc-panel-step>
			<hc-image :preview="true" slot="icon" :src="$hc.Command.GetAttachmentURL(item.HeadImgUrl)" :imageStyle="imageStyle"></hc-image>
			<view slot="content" class="item-step">
				<view class="item-step-title">
					<view style="flex:1;">
						<view style="font-weight: bold; font-size: 28rpx;">{{item.FFOLLOW_UP_MAN_NAME||""}}</view>
						<view style=" font-size:22rpx ;">{{ item.FFOLLOW_DATE_TIME_DESC||"" }}</view>
					</view>
					<view style="width:250rpx;text-align: right;">
						<view v-if="item.FFOLLOW_UP_WAY_DESC">
							<hc-icon icon="icon-goutong" :size="30" style="padding-right: 10rpx;"></hc-icon>
							<text style="font-size: 28rpx;">{{item.FFOLLOW_UP_WAY_DESC||""}}</text>
						</view>
					</view>
					<view style="width:250rpx;text-align: right;">
						<view>
							<hc-icon icon="icon-shijian" :size="30" style="padding-right: 5rpx;"></hc-icon>
							<text style="font-size: 28rpx;">{{item.FFOLLOW_TIMES||"0"}}分钟</text>
						</view>
					</view>
				</view>
				<view class="item-step-content" v-if="item.FCONTENT" style="font-size: 28rpx;">{{item.FCONTENT||""}}</view>
				<view class="item-step-image" v-if="getImage(item.RecordAttList).length>0">
					<!-- 多个 -->
					<view class="hc-upload-image-multiple">
						<view class="hc-upload-image-item" style="margin-right: 20rpx; margin-bottom: 20rpx; font-size: 0;" v-for="(img, index) in getImage(item.RecordAttList)"
						 :key="img.FATTACHMENT_ID">
							<hc-image :preview="true" :previewUrls="getUrls(getImage(item.RecordAttList))" :src="getUrl(img)"></hc-image>
						</view>
					</view>
				</view>
				<view class="item-step-voice" v-if="getVoice(item.RecordAttList).length>0">
					<view class="item-step-voice-item" v-for="audio in getVoice(item.RecordAttList)" :key="audio.FATTACHMENT_ID">
						<hc-audio :style="audioStyle" :duration="audio.FRECORD_TIMES" :size="50" :src="$hc.Command.GetAttachmentURL(audio.FATTACHMENT_ID, false)"></hc-audio>
					</view>
				</view>
				<view class="item-step-att" v-if="getOtherAtt(item.RecordAttList).length>0">
					<view class="item-step-att-item" v-for="otherFile in getOtherAtt(item.RecordAttList)" :key="otherFile.FATTACHMENT_ID">
						<hc-tag icon="icon-fujian" :text="otherFile.FATTACHMENT_NAME" @click="openFile(otherFile.FATTACHMENT_ID)"></hc-tag>
					</view>
				</view>
			</view>
		</hc-panel-step>
		<hc-gap :bg-color="boColor" height="5"></hc-gap>
	</view>
</template>

<script>
	/*
	 * @example <hc-follow-up item="{}" index="0"></hc-follow-up>
	 */
	export default {
		name: "hc-follow-up",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			imageStyle: {
				type: Object,
				default () {
					return {
						width: "50rpx",
						height: "50rpx",
						borderRadius: "50%"
					}
				}
			},
		},
		data() {
			return {
				boColor: this.$hc.color.bgColor,
				audioStyle: {
					paddingRight: '30rpx'
				}
			};
		},
		methods: {
			Tap() {
				this.$emit("click", this.item)
			},
			getUrl(record) {
				return this.$hc.Command.GetAttachmentURL(record.FATTACHMENT_ID);
			},
			getUrls(lsRecord) {
				var _this = this;
				return lsRecord.map(function(item) {
					return _this.$hc.Command.GetAttachmentURL(item.FATTACHMENT_ID);
				});
			},

			//获取附件图片
			getImage(source) {
				if (source && Array.isArray(source)) {
					return source.filter(item => {
						return item.FATTACHMENT_TYPE == '2'
					});
				}
				return [];
			},
			//获取附件音频
			getVoice(source) {
				if (source && Array.isArray(source)) {
					return source.filter(item => {
						return item.FATTACHMENT_TYPE == '1'
					});
				}

				return [];
			},
			//获取其他附件
			getOtherAtt(source) {
				if (source && Array.isArray(source)) {
					return source.filter(item => {
						return (item.FATTACHMENT_TYPE != '1' && item.FATTACHMENT_TYPE != '2')
					});
				}
				return [];
			},
			getDate(date) {
				return this.$hc.moment(date).format("YYYY-MM-DD HH:mm");
			},

			openFile(fileId) {
				if (!this.$hc.Command.IsEmpty(fileId)) {
					var file = this.$hc.Command.GetAttachmentURL(fileId);
					uni.openDocument({
						filePath: file
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		padding-left: 10rpx;
		//margin-top: 10rpx;
		padding-right: 15rpx;
		border-top: 1rpx solid #EEF1F5;
		line-height: 60rpx;
		//box-shadow: 1px 1px 1px 1px rgb(221, 221, 221);

		&-step {
			margin-left: 10rpx;
			margin-top: 30rpx;
			border-radius: 10px;
			background-color: #fff;
			box-shadow: 0 2px 12px 0 rgba(0,0,0,.1); 
			padding-bottom: 10rpx;

			&-title {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				// justify-content: left;
				// align-items: center;
				padding: 10rpx;
				// background-color: #EEEEEE;
				line-height: 1.5;
				border-radius: 10px 10px 0 0;
				margin-left: 0rpx !important;
				margin-right: 0rpx !important;
				font-size: 28rpx;
			}

			&-image {
				padding: 10rpx 10rpx 0 10rpx;
			}

			&-content {
				overflow: hidden;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				padding: 10rpx;
			}

			&-voice {
				margin: 10rpx;
				padding-left: 10rpx;

				&-item {
					display: flex;
					flex: 1;
					line-height: 1;
					vertical-align: top;
					position: relative;
					margin-bottom: 20rpx;
					width: 100%;

					&-close {
						top: 0rpx !important;
						right: 20rpx !important;
					}
				}
			}

			&-att {
				display: flex;
				flex-wrap: wrap;
				padding-bottom: 20rpx;
				padding-left: 10rpx;

				&-item {
					padding: 0rpx 10rpx 10rpx;
				}
			}
		}


	}

	.hc-upload-image-multiple {
		line-height: 1.5;
	}

	.hc-upload-image-item {
		display: inline-block;
		line-height: 1;
		vertical-align: top;
		position: relative;
		width: 160rpx;
		height: 160rpx;
	}
</style>
