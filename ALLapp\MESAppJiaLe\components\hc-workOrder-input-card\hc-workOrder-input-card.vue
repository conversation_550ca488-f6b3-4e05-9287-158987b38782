<template>
	<view class="outBox">
		<view class="item" v-if="item" :key="index">
			<view style="margin: 10rpx;overflow: auto;" >
				<hc-form style="border-radius: 20rpx;">
					<!-- <hc-form-item label="工单编号" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FWORK_ORDER_NO" input-align="right" maxlength="50"
							placeholder="" ref="FWORK_ORDER_NO" disabled :clearable="false">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="产品编号" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FMATERIAL_CODE" input-align="right" maxlength="50"
							placeholder="" ref="FMATERIAL_CODE" disabled :clearable="false">
						</hc-input>
					</hc-form-item> -->
					<hc-form-item label="产品名称" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FMATERIAL_NAME" input-align="right" maxlength="50"
							placeholder="" ref="FMATERIAL_NAME" disabled :clearable="false">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="产品规格" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FSPEC_DESC" input-align="right" maxlength="50"
							placeholder="" ref="FSPEC_DESC" disabled :clearable="false">
						</hc-input>
					</hc-form-item>
					<!-- <hc-form-item label="加工工艺" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FCRAFT_NAME" input-align="right" maxlength="50"
							placeholder="" ref="FCRAFT_NAME" disabled :clearable="false">
						</hc-input>
					</hc-form-item> -->
					<hc-form-item label="加工工位" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FSTATION_NAME" input-align="right" maxlength="50"
							placeholder="请选择加工工位" ref="FSTATION_NAME" disabled :clearable="false"
							@tap="changeSelect(item,index,'FSTATION_NAME','FSTATION_ID')">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="实际开工时间" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FACT_ST_DATE" input-align="right" maxlength="50"
							placeholder="请选择实际开工时间" @tap="changeTime(item,index,'FACT_ST_DATE')" ref="FACT_ST_DATE"
							:disabled="true" :clearable="false">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="实际完工时间" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FACT_ED_DATE" input-align="right" maxlength="50"
							placeholder="请选择实际完工时间" @tap="changeTime(item,index,'FACT_ED_DATE')" ref="FACT_ED_DATE"
							:disabled="true" :clearable="false">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="合格数量" label-width="auto" prop="">
						<hc-col :span="10">
							<hc-input type="number" v-model="item.FPASS_QTY" input-align="right" maxlength="50"
								placeholder="请输入合格数量" ref="FPASS_QTY" :clearable="false" dataColor="warn">
							</hc-input>
						</hc-col>
						<hc-col :span="2" style="text-align: right;">
							{{item.FPRO_UNIT_NAME}}
						</hc-col>
					</hc-form-item>
					<hc-form-item label="不良数量" label-width="auto" prop="">
						<hc-col :span="10">
							<hc-input type="number" v-model="item.FNG_QTY" input-align="right" maxlength="50"
								placeholder="请输入不良数量" ref="FNG_QTY" :clearable="false" dataColor="warn">
							</hc-input>
						</hc-col>
						<hc-col :span="2" style="text-align: right;">
							{{item.FPRO_UNIT_NAME}}
						</hc-col>
					</hc-form-item>
					<hc-form-item label="不良原因" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FBAD_REASON_NAME" input-align="right" maxlength="50"
							placeholder="请选择不良原因" ref="FBAD_REASON_NAME" :disabled="true" :clearable="false"
							@tap="changeSelect(item,index,'FBAD_REASON_NAME','FBAD_REASON_ID')">
						</hc-input>
					</hc-form-item>
				</hc-form>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "hc-workOrder-input-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
				},
			};
		},
		computed: {

		},
		methods: {
			//选择下拉
			changeSelect(item, index, name, id) {
				this.$emit("updateSelect", {
					name,
					id,
					item,
					index
				})
			},
			//选择时间
			changeTime(item, index, str) {
				this.$emit("updateTime", {
					str,
					item,
					index
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.outBox {
		background-color: #eef1f5;

		.item {
			margin-top: 5rpx;
			margin-left: 5rpx;
			margin-right: 5rpx;
		}
	}
</style>
