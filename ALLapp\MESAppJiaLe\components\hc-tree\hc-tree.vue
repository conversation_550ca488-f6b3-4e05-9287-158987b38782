<template>
	<hc-uni-swipe-action v-if="visible">
		<hc-uni-swipe-action-item v-for="item in formatData" :key="item[nodeKey]" v-if="getShow(item)" :disabled="!canEdit(item)"
		 :right-options="editOptions(item)" @click="operateClickHandle($event, item)">
			<view :style="{marginLeft: `${item.level * 40}rpx`, height: itemHeight + 'rpx', color: getSelected(item) ? $themeCurrent.main : ''}"
			 @tap.stop="tapHandle(item)" class="hc-tree-item">
				<view class="hc-tree-item-text">
					<slot name="render" :record="item" v-if="hasRenderScopedSlots"></slot>
					<text style="margin-right: 10rpx;" v-else>{{item[titleKey]}}</text>
					<hc-icon v-if="getSelected(item)" size="32" name="checkmark-circle" :color="$themeCurrent.main"></hc-icon>
				</view>
				<view class="hc-tree-item-toggle" @tap.stop="toggle(item)">
					<hc-icon v-if="!item.isLeaf" :name="getOpen(item) ? 'arrow-up' : 'arrow-down'"></hc-icon>
				</view>
			</view>
		</hc-uni-swipe-action-item>
	</hc-uni-swipe-action>
</template>

<script>
	/**
	 * tree 树 无限级
	 * @description 可进行 节点 展开收起 选择 支持侧滑操作
	 * @property {Array} openKeys 展开的节点keys
	 * @property {Array} selectedKeys 选中的节点keys
	 * @property {Array} source 数据源
	 * @property {String} titleKey title属性名
	 * @property {String} nodeKey 唯一键属性名
	 * @property {String} childrenKey children属性名
	 * @property {String} editMode 编辑模式
	 * @property {Function} editOptions 自定义侧滑操作选项
	 * @property {String} selectedMode 选择模式 多选 单选
	 * @property {Function} editFilter 节点是否可编辑 过滤器（优先级高于editMode）
	 * @event {Function} selectedChange 节点选择取消选择时触发
	 * @event {Function} openChange 节点展开收起时触发
	 * @event {Function} operateClick 侧滑操作选项点击时触发
	 * @example <hc-tree :source="source"></hc-tree>
	 */
	export default {
		data() {
			return {
				visible: true,
				formatData: [],
				useOpenNodes: [],
				useSelectedNodes: []
			};
		},
		inject: {
			// 修复 uni-app v-if 为false时 在微信小程序下 插槽代码依旧执行的问题
			popup: {
				from: "popup",
				default: () => ({
					showDrawer: true
				})
			}
		},
		props: {
			titleKey: {
				type: String,
				default: "title"
			},
			nodeKey: {
				type: String,
				default: "id"
			},
			childrenKey: {
				type: String,
				default: "children"
			},
			itemHeight: {
				type: Number,
				default: 70
			},
			openKeys: {
				type: Array,
				default () {
					return []
				}
			},
			selectedKeys: {
				type: Array,
				default () {
					return []
				}
			},
			source: {
				type: Array,
				default () {
					return []
				}
			},
			// 编辑模式 none(不可编辑) any(任意节点都可编辑) leaf(叶子节点可编辑) notLeaf(非叶子节点可编辑)
			editMode: {
				type: String,
				default: "none"
			},
			// 自定义侧滑操作选项 节点数据为入参
			editOptions: {
				type: Function,
				default () {
					// #ifdef H5
					return []
					// #endif
					// #ifndef H5
					return record => []
					// #endif
				}
			},
			// 选择模式  多选 单选
			selectedMode: {
				type: String,
				default: "multiple" // single
			},
			// 节点是否可编辑 过滤器
			editFilter: {
				type: Function,
				default () {
					return record => true
				}
			}
		},
		created() {
			this.useOpenNodes = [...this.openKeys];
			this.useSelectedNodes = [...this.selectedKeys];
			this.setExtraOpenKeys(this.formatData, this.selectedKeys);
		},
		watch: {
			"popup.showDrawer": {
				handler(newVal) {
					this.visible = newVal;
				},
				immediate: true
			},
			openKeys(newVal) {
				this.useOpenNodes = [...newVal];
			},
			selectedKeys(newVal) {
				this.setExtraOpenKeys(this.formatData, newVal);
				this.useSelectedNodes = [...newVal];
			},
			source: {
				handler: function(newVal) {
					this.formatData = this.loopExpand(newVal);
				},
				immediate: true
			},
			formatData(newVal) {
				this.setExtraOpenKeys(newVal, this.selectedKeys);
			}
		},
		methods: {
			operateClickHandle(e, record) {
				this.$emit("operateClick", e, record);
			},
			canEdit(record) {
				if (typeof this.editFilter === "function") {
					return this.editFilter(record);
				}
				return this.editMode === 'any' || (this.editMode === 'leaf' && record.isLeaf) || (this.editMode === 'notLeaf' &&
					!record.isLeaf)
			},
			setExtraOpenKeys(formatData, selectedKeys) {
				const {
					useOpenNodes,
					nodeKey
				} = this;
				if (formatData.length && selectedKeys.length) {
					let result = [];
					selectedKeys.forEach(key => {
						const record = formatData.find(item => item[nodeKey] === key);
						if (record) {
							const parentIds = record.path.slice(0, -1);
							const missKeys = parentIds.filter(key => useOpenNodes.indexOf(key) === -1);
							result = result.concat(missKeys);
						}
					});
					this.useOpenNodes = [...this.useOpenNodes, ...result];
				}
			},
			tapHandle(record) {
				const key = record[this.nodeKey];
				const index = this.useSelectedNodes.indexOf(key);
				if (index > -1) {
					this.useSelectedNodes.splice(index, 1)
				} else {
					this.selectedMode === "multiple" ? this.useSelectedNodes.push(key) : this.useSelectedNodes = [key];
				}
				this.$emit("selectedChange", [...this.useSelectedNodes], this.formatData.filter(item => this.useSelectedNodes.indexOf(
					item[this.nodeKey]) > -1));
			},
			getShow(record) {
				if (record.level === 0) return true;
				// 之上路径 都已展开
				return record.path.slice(0, -1).every(item => this.useOpenNodes.indexOf(item) > -1);
			},
			getSelected(record) {
				return this.useSelectedNodes.includes(record[this.nodeKey]);
			},
			toggle(record) {
				const key = record[this.nodeKey];
				const index = this.useOpenNodes.indexOf(key);
				index > -1 ? this.useOpenNodes.splice(index, 1) : this.useOpenNodes.push(key);
				this.$emit("openChange", [...this.useOpenNodes]);
			},
			getOpen(record) {
				return this.useOpenNodes.includes(record[this.nodeKey]);
			},
			loopExpand(data) {
				// 防止大数据量爆栈 广度优先 平铺数据
				const {
					nodeKey,
					childrenKey
				} = this;
				let queue = data.map(record => {
					const children = record[childrenKey];
					return {
						level: 0,
						path: [record[nodeKey]],
						isLeaf: children && children.length ? false : true,
						...record
					}
				});
				let result = [];
				while (queue.length) {
					const record = queue.shift();
					result.push(record);
					const children = record[childrenKey];
					if (children && children.length) {
						// 往前插入
						queue = children.map(child => {
							const children = child[childrenKey];
							return {
								parent: record,
								path: record.path.concat([child[nodeKey]]),
								level: record.level + 1,
								isLeaf: children && children.length ? false : true,
								...child
							}
						}).concat(queue)
					}
				}
				return result;
			}
		},
		computed: {
			hasRenderScopedSlots() {
				return this.$scopedSlots.render ? true : false;
			}
		}
	}
</script>

<style lang="scss">
	.hc-tree-item {
		min-width: 0;
		flex: 1;
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: $hc-content-color;
		padding-left: 20rpx;
		border-bottom: 1px solid $uni-border-color;

		&-text {
			flex: 1;
			min-width: 0;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			padding-right: 16rpx;
		}

		&-toggle {
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 50rpx;
		}
	}
</style>
