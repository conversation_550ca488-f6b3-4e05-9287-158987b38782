﻿<template>
  <view class="hc-order-flow-date-status" 
	:class="[listItem?'list-item':'']">
    <view class="hc-order-flow-date-status-item" :class="getClassName">
      <span v-if="!hiddenStatus" class="hc-order-flow-date-status-item-status">
		<hc-icon name="checkmark-circle" v-if="source[statusKey] === statusMap.finish"></hc-icon>
        <hc-icon icon="icon-Ding" v-if="isPending"></hc-icon>
        <hc-icon
          icon="icon-wait"
          v-if="source[statusKey] === statusMap.beforeNormal || source[statusKey] === statusMap.beforeTimeout"
        ></hc-icon>
        {{source[textKey]}}
      </span>
      <span
        class="hc-order-flow-date-status-item-text"
        :title="source[contentKey]"
        @click="textClickHandle"
      >{{source[contentKey]}}</span>
    </view>
  </view>
</template>

<script>
export default {
  name: "hc-order-flow-date-status",
  props: {
    source: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 外部可自行设置状态码
    statusMap: {
      type: Object,
      default: function() {
        // 未到（正常）
        // 未到（超时）
        // 待处理（正常）
        // 待处理（超时）
        // 已完成
        return {
          beforeNormal: 0,
          beforeTimeout: 1,
          pendingNormal: 2,
          pendingTimeout: 3,
          finish: 4
        };
      }
    },
    // 状态key
    statusKey: {
      type: String,
      default: "status"
    },
    // 左侧文字key
    textKey: {
      type: String,
      default: "text"
    },
    // 右侧文字key
    contentKey: {
      type: String,
      default: "content"
    },
    // 隐藏左侧状态
    hiddenStatus: Boolean,
	listItem:{
	  type: Boolean,
	  default: false
	},
  },
  computed: {
    isPending: function() {
      return (
        this.source[this.statusKey] === this.statusMap.pendingNormal ||
        this.source[this.statusKey] === this.statusMap.pendingTimeout
      );
    },
    getClassName: function() {
      var result = "";
      switch (this.source[this.statusKey]) {
        case this.statusMap.beforeNormal:
          result = "hc-order-flow-date-status-item-beforeNormal";
          break;
        case this.statusMap.beforeTimeout:
          result = "hc-order-flow-date-status-item-beforeTimeout";
          break;
        case this.statusMap.pendingNormal:
          result = "hc-order-flow-date-status-item-pendingNormal";
          break;
        case this.statusMap.pendingTimeout:
          result = "hc-order-flow-date-status-item-pendingTimeout";
          break;
        case this.statusMap.finish:
          result = "hc-order-flow-date-status-item-finish";
          break;
      }
      return result;
    }
  },
  methods: {
    buttonClickHandle: function() {
      // 只有待处理才处理
      if (this.isPending) {
        this.$emit("button-click", this.source);
      }
    },
    textClickHandle: function() {
      this.$emit("text-click", this.source);
    }
  }
};
</script>

<style lang="scss" scoped>
.hc-order-flow-date-status {
  height: auto;
  padding: 2px 0;
  line-height: 1;
  font-size: 14px;
}

.hc-order-flow-date-status-item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}
.hc-order-flow-date-status-item > .hc-order-flow-date-status-item-status {
  border-radius: 4px;
  padding: 2px 4px;
  margin-right: 4px;
  -ms-flex-preferred-size: 58px;
}

.hc-order-flow-date-status-item > .hc-order-flow-date-status-item-text {
  -ms-flex: 1;
  flex: 1;
  text-decoration: underline;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

.hc-order-flow-date-status-item-beforeNormal {
  color: #59677b;
}
.hc-order-flow-date-status-item-beforeNormal
  > .hc-order-flow-date-status-item-status {
  border: 1px solid #59677b;
}
.hc-order-flow-date-status-item-beforeTimeout {
  color: #f54053;
}
.hc-order-flow-date-status-item-beforeTimeout
  > .hc-order-flow-date-status-item-status {
  border: 1px solid #f54053;
}
.hc-order-flow-date-status-item-pendingNormal {
  color: #408ef5;
}
.hc-order-flow-date-status-item-pendingNormal
  > .hc-order-flow-date-status-item-status {
  background-color: #408ef5;
  color: #fff;
}
.hc-order-flow-date-status-item-pendingTimeout {
  color: #f54053;
}
.hc-order-flow-date-status-item-pendingTimeout
  > .hc-order-flow-date-status-item-status {
  background-color: #f54053;
  color: #fff;
}
.hc-order-flow-date-status-item-finish {
  color: #2cc180;
}

.hc-order-flow-date-status-item-finish
  > .hc-order-flow-date-status-item-status {
  border: 1px solid #2cc180;
}


.list-item {
	height: auto !important;
	padding-left: 30rpx;
	padding-top: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid $hc-border-color;
}

</style>
