<template>
	<view class="item" @click="$emit('click', item, index)">
		<view class="item-content">
			<material-info-display 
				:item="item" 
				:edit-data="editData" 
			/>
			<view v-if="showTagButton" class="tag-section">
				<text class="tag-count">标签数：{{ item.TagNum || 0 }}</text>
				<hc-button 
					type="primary" 
					shape="square" 
					size="medium"
					class="add-tag-btn" 
					@click="$emit('add-tag', item)"
				>
					添加标签
				</hc-button>
			</view>
		</view>
	</view>
</template>

<script>
import MaterialInfoDisplay from './material-info-display.vue'

/**
 * 物料项目卡片组件
 * 用于显示物料清单中的单个物料项目
 */
export default {
	name: "material-item-card",
	
	components: {
		MaterialInfoDisplay
	},
	
	props: {
		item: {
			type: Object,
			required: true
		},
		index: {
			type: Number,
			required: true
		},
		editData: {
			type: Object,
			required: true
		},
		showTagButton: {
			type: Boolean,
			default: false
		}
	}
}
</script>

<style lang="scss" scoped>
.item {
	width: 100%;
	display: flex;
	padding: 15rpx 12rpx;
	border-bottom: 2rpx solid #d1d1d1;
	background-color: #ffffff;
	
	&-content {
		flex: 1;
	}
}

.tag-section {
	display: flex;
	align-items: center;
	margin-top: 10rpx;
	
	.tag-count {
		font-size: 26rpx;
		color: #666;
	}
	
	.add-tag-btn {
		margin-left: 20rpx;
	}
}
</style>
