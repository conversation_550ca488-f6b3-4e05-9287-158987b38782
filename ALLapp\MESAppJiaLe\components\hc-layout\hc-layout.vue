﻿<template>
	<view class="hc-layout hc-skeleton" :style="[style]">
		<slot></slot>
		<hc-skeleton v-if="enableSkeleton" :loading="skeletonVisible" animation></hc-skeleton>
	</view>
</template>

<script>
	/**
	 * layout 布局
	 * @description 此组件在页面需要进行分栏布局时使用
	 * @example <hc-layout></hc-layout>
	 */
	export default {
		name: "hc-layout",
		provide() {
			return {
				layout: this
			}
		},
		props: {
			// 开启灰色背景
			enableBackground: {
				type: Boolean,
				default: true
			},
			// 开启骨架屏
			enableSkeleton: {
				type: Boolean,
				default: true
			},
			// 是否显示骨架屏
			skeletonVisible: {
				type: Boolean,
				default: true
			}
		},
		computed: {
			style() {
				let result = {};
				if (this.enableBackground) result.backgroundColor = this.$hc.color.bgColor;
				return result;
			}
		},
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss" scoped>
	.hc-layout {
		height: 100%;
		display: flex;
		flex-direction: column;
		min-width: 0;
		min-height: 0;
		overflow: hidden;
	}
</style>
