<template>
	<view class="item" v-if="item" :key="index">
		<view class="item-left">
			<hc-image :src="$hc.Command.GetAttachmentURL(item.FPIC_ATTACH_ID)"></hc-image>
		</view>
		<view style="flex: 1;" class="item-content">
			<hc-row>
				<hc-col :span="12">
					<view class="item-content">
						<!-- <view class="item-content-type pro-left">物料编号:{{ item.FMATERIAL_CODE||"" }}</view>
						<view class="item-content-type pro-left">物料名称:{{ item.FMATERIAL_NAME||"" }}</view>
						<view class="item-content-type pro-left">规格:{{ item.FSPEC_DESC||"" }}</view>
						<view class="item-content-type pro-left">单位:{{ item.FPUR_UNIT_NAME||"" }}</view>
						<view class="item-content-type pro-left">订单数量:{{ item.FCAN_STOCKING_QTY||"" }}</view>
						<view class="item-content-type pro-left">入库数量:<hc-input></hc-input></view> -->
						<hc-row>
							<hc-col :span="12">
								<view class="item-content-title pro-left">名称：{{ item.FMATERIAL_NAME||"" }}</view>
							</hc-col>
						</hc-row>
						<hc-row>
							<hc-col :span="12">
								<view class="item-content-type pro-left">编号：{{ item.FMATERIAL_CODE||"" }}</view>
							</hc-col>
						</hc-row>
						<hc-row>
							<hc-col :span="12">
								<view class="item-content-type pro-left">规格：{{ item.FSPEC_DESC||"" }}</view>
							</hc-col>
						</hc-row>
						<hc-row>
							<hc-col :span="12">
								<view class="item-content-type pro-left">现存量：{{ item.FSTK_UNIT_QTY||0 }}</view>
							</hc-col>
						</hc-row>

					</view>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	/*
	 * @example <hc-material-recv-card item="{}" index="0"></hc-material-recv-card>
	 */
	export default {
		name: "hc-material-stockDetailCheck-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
			editData: {
				type: Object,
				default () {
					return {
						title: "",
						field: '',
						// fieldQty:'',
					}
				}
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				}
			};
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			},

			// change(value) {
			// 	debugger
			// 	console.log("---value", value)
			// 	// this.item.FSTOCKING_QTY = value
			// 	this.$emit("ChildChange", value)
			// }
		}
	}
</script>

<style lang="scss">
	.item {
		// width: 100%;
		display: flex;
		padding: 15rpx 12rpx;

		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 20rpx;
			min-width: 200rpx;
			max-width: 200rpx;
			height: 200rpx;
		}

		&-content {
			&-title {
				font-size: 32rpx;
				line-height: 50rpx;
				font-weight: bold;
			}

			&-type {
				margin: 10rpx 0;
				font-size: 28rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-left {
		// width: 500rpx;
		//width: calc(100%/12);
		width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
