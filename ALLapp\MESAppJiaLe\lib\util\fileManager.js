﻿/**
 * 文件管理器
 * @description 调起文件管理器 支持 H5 Android iOS
 * @param {Function} callback(platform, res)
 **/
export default (callback) => {
	// #ifdef H5
	const inputEle = document.createElement("input");
	inputEle.type = "file";
	inputEle.style.display = "none";
	inputEle.setAttribute('multiple','multiple')
	inputEle.onchange = (e) => {
		typeof callback === "function" && callback("h5", e.target.files);
	}
	document.body.appendChild(inputEle);
	inputEle.click();
	document.body.removeChild(inputEle);
	// #endif

	// #ifdef APP-PLUS
	const platform = plus.os.name.toLowerCase();
	if (platform === "android") {
		let that = this
		// java 代码来自 http://www.cnblogs.com/panhouye/archive/2017/04/23/6751710.html
		let main = plus.android.runtimeMainActivity();
		let Intent = plus.android.importClass("android.content.Intent");

		// 
		let fileIntent = new Intent(Intent.ACTION_GET_CONTENT)
		//fileIntent.setType(“image/*”);//选择图片
		//fileIntent.setType(“audio/*”); //选择音频
		//fileIntent.setType(“video/*”); //选择视频 （mp4 3gp 是android支持的视频格式）
		//fileIntent.setType(“video/*;image/*”);//同时选择视频和图片
		fileIntent.setType("*/*"); //无类型限制
		fileIntent.addCategory(Intent.CATEGORY_OPENABLE);
		main.startActivityForResult(fileIntent, 1);
		// 获取回调
		main.onActivityResult = function(requestCode, resultCode, data) {
			let Activity = plus.android.importClass("android.app.Activity");
			let ContentUris = plus.android.importClass("android.content.ContentUris");
			let Cursor = plus.android.importClass("android.database.Cursor");
			let Uri = plus.android.importClass("android.net.Uri");
			let Build = plus.android.importClass("android.os.Build");
			let Environment = plus.android.importClass("android.os.Environment");
			let DocumentsContract = plus.android.importClass("android.provider.DocumentsContract");
			let MediaStore = plus.android.importClass("android.provider.MediaStore");
			// 给系统导入 contentResolver
			let contentResolver = main.getContentResolver()
			plus.android.importClass(contentResolver);
			// 返回路径
			let path = '';
			if (resultCode == Activity.RESULT_OK) {
				let uri = data.getData()
				if ("file" == uri.getScheme().toLowerCase()) { //使用第三方应用打开
					path = uri.getPath();
				}
				if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT) { //4.4以后
					path = getPath(this, uri);
				} else { //4.4以下下系统调用方法
					path = getRealPathFromURI(uri)
				}
				// 回调
				typeof callback === "function" && callback("android", path);
			}
			// 4.4 以上 从Uri 获取文件绝对路径
			function getPath(context, uri) {
				let isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;
				let scheme = uri.getScheme().toLowerCase()
				if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
					// ExternalStorageProvider
					if (isExternalStorageDocument(uri)) {
						let docId = DocumentsContract.getDocumentId(uri);
						let split = docId.split(":");
						let type = split[0];
						// 如果是手机内部存储
						if ("primary" == type.toLowerCase()) {
							return Environment.getExternalStorageDirectory() + "/" + split[1];
						} else {
							return '/storage/' + type + "/" + split[1];
						}
					}
					// DownloadsProvider
					else if (isDownloadsDocument(uri)) {
						let id = DocumentsContract.getDocumentId(uri);
						let split = id.split(":");
						return split[1]
						// console.log(id)
						// let contentUri = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), id);
						// return getDataColumn(context, contentUri, null, null);
					}
					// MediaProvider
					else if (isMediaDocument(uri)) {
						let docId = DocumentsContract.getDocumentId(uri);
						let split = docId.split(":");
						let type = split[0];
						let contentUri = null;
						if ("image" == type.toLowerCase()) {
							contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
						} else if ("video" == type.toLowerCase()) {
							contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
						} else if ("audio" == type.toLowerCase()) {
							contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
						}
						let selection = "_id=?";
						let selectionArgs = [split[1]];
						return getDataColumn(context, contentUri, selection, selectionArgs);
					}
				}
				// MediaStore (and general)
				else if ("content" == scheme) {
					return getDataColumn(context, uri, null, null);
				}
				// File
				else if ("file" == scheme) {
					return uri.getPath();
				}
			}
			// 4.4 以下 获取 绝对路径
			function getRealPathFromURI(uri) {
				let res = null
				let proj = [MediaStore.Images.Media.DATA]
				let cursor = contentResolver.query(uri, proj, null, null, null);
				if (null != cursor && cursor.moveToFirst()) {;
					let column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
					res = cursor.getString(column_index);
					cursor.close();
				}
				return res;
			}
			// 通过uri 查找出绝对路径
			function getDataColumn(context, uri, selection, selectionArgs) {
				let cursor = null;
				let column = "_data";
				let projection = [column];
				// let contentResolver = context.getContentResolver()
				// plus.android.importClass(contentResolver);
				cursor = contentResolver.query(uri, projection, selection, selectionArgs, null);
				if (cursor != null && cursor.moveToFirst()) {
					let column_index = cursor.getColumnIndexOrThrow(column);
					return cursor.getString(column_index);
				}
			}

			function isExternalStorageDocument(uri) {
				return "com.android.externalstorage.documents" == uri.getAuthority() ? true : false
			}

			function isDownloadsDocument(uri) {
				return "com.android.providers.downloads.documents" == uri.getAuthority() ? true : false
			}

			function isMediaDocument(uri) {
				return "com.android.providers.media.documents" == uri.getAuthority() ? true : false
			}
		}
	} else if (platform === "ios") {
		const iOSFileSelect = uni.requireNativePlugin('YangChuan-YCiOSFileSelect');
		//apple document-types 文件类型参数 https://developer.apple.com/library/archive/documentation/Miscellaneous/Reference/UTIRef/Articles/System-DeclaredUniformTypeIdentifiers.html
		let params = {
			"document-types": ["public.text", "public.zip", "public.data", "com.adobe.pdf", "com.microsoft.word.doc",
				"com.adobe.postscript", "com.microsoft.excel.xls", "com.adobe.encapsulated- postscript",
				"com.microsoft.powerpoint.ppt", "com.adobe.photoshop- image", "com.microsoft.word.rtf",
				"com.microsoft.advanced- systems-format", "com.microsoft.advanced- stream-redirector"
			],
			"isBase64": 0
		}
		iOSFileSelect.show(params, result => {
			let status = parseInt(result.status);
			if (status == 200) {
				let url = result.url;
				uni.downloadFile({
					url: url,
					success: function(res) {
						if (res.statusCode == 200) {
							typeof callback === "function" && callback("ios", res.tempFilePath);
						}
					}
				});
			}
		});
	}
	// #endif
}
