﻿<template>
	<scroll-view scroll-x :scrollLeft="scrollLeft" class="hc-order-steps">
		<view class="hc-order-steps-top">
			<view class="hc-order-steps-top-item" v-for="(item,index) in source" :key="index">
				<view class="hc-order-steps-top-item-before" :style="[lineStyle(item)]" :class="[
					index === 0 ? 'hc-order-steps_opacity' : ''
				]"></view>
				<view @tap="itemClickHandle(item, index, extraIndex)">
					<view v-if="item[statusKey] === statusMap.beforeNormal" :style="{borderColor: borderColor }" class="hc-order-steps_circle">
						<hc-icon size="40" icon="icon-wait"></hc-icon>
						<text style="margin-left: 6rpx;">{{$hc.Command.FormatMonDate(item[dateKey])}}</text>
					</view>
					<view v-else-if="item[statusKey] === statusMap.beforeTimeout" :style="{borderColor: errorColor, color: errorColor }"
					 class="hc-order-steps_circle">
						<hc-icon size="40" icon="icon-wait" :color="errorColor"></hc-icon>
						<text style="margin-left: 6rpx;">{{$hc.Command.FormatMonDate(item[dateKey])}}</text>
					</view>
					<view id="pending" v-else-if="item[statusKey] === statusMap.pendingNormal || item[statusKey] === statusMap.pendingTimeout"
					 :style="{backgroundColor: !item.HasPermission ? disabledColor : item[statusKey] === statusMap.pendingNormal ? $themeCurrent.main : errorColor}"
					 class="hc-order-steps_pending">{{item[textKey]}}</view>
					<view v-else-if="item[statusKey] === statusMap.finishCanCancel" :style="{backgroundColor: !item.HasPermission ? disabledColor : null }"
					 class="hc-order-steps_finishCanCancel">
						<hc-icon size="40" name="checkmark-circle"></hc-icon>
						<text style="margin-left: 6rpx;">{{$hc.Command.FormatMonDate(item[dateKey])}}</text>
					</view>
					<view v-else-if="item[statusKey] === statusMap.finishForbidCancel" :style="{borderColor: !item.HasPermission ? disabledColor : null, color: !item.HasPermission ? disabledColor : null}"
					 class="hc-order-steps_finishForbidCancel">
						<hc-icon :color="!item.HasPermission ? disabledColor : successColor" size="40" name="checkmark-circle"></hc-icon>
						<text style="margin-left: 6rpx;">{{$hc.Command.FormatMonDate(item[dateKey])}}</text>
					</view>
					<view style="margin-top: 10rpx;">{{item[textKey]}}</view>
				</view>
				<view class="hc-order-steps-top-item-after" :style="[lineStyle(item)]" :class="[
					index === source.length -1 ? 'hc-order-steps_opacity' : ''
				]"></view>
			</view>
		</view>
	</scroll-view>
</template>

<script>
	const systemInfo = uni.getSystemInfoSync();
	const windowWidth = systemInfo.windowWidth;
	export default {
		data() {
			return {
				scrollLeft: 0,
				successColor: this.$hc.color.success,
				errorColor: this.$hc.color.error,
				borderColor: this.$hc.color.borderColor,
				disabledColor: "#ddd",
			};
		},
		props: {
			// 外部可自行设置状态码
			statusMap: {
				type: Object,
				default: function() {
					// 未到（正常）
					// 未到（超时）
					// 待处理（正常）
					// 待处理（超时）
					// 已完成（可撤销）
					// 已完成（不可撤销）
					return {
						beforeNormal: 0,
						beforeTimeout: 1,
						pendingNormal: 2,
						pendingTimeout: 3,
						finishCanCancel: 4,
						finishForbidCancel: 5,
					};
				}
			},
			source: {
				type: Array,
				default () {
					return []
				}
			},
			statusKey: {
				type: String,
				default: "status"
			},
			textKey: {
				type: String,
				default: "text"
			},
			dateKey: {
				type: String,
				default: "PlanDate"
			},
			extraIndex: {
				type: Number,
				default: 0
			}
		},
		watch: {
			source: {
				handler(newVal) {
					// （由于只能获取 节点的边界坐标） 先重置
					this.scrollLeft = 0;
					this.$nextTick(() => {
						const node = uni.createSelectorQuery().in(this).select(".hc-order-steps_pending");
						node.boundingClientRect(data => {
							const {
								left,
								width
							} = data || {};
							if (left > windowWidth) {
								this.scrollLeft = left + width / 2 - windowWidth / 2;
							}
						}).exec();
					})
				},
				immediate: true,
				deep: true
			}
		},
		methods: {
			lineStyle(item) {
				const status = item[this.statusKey];
				const {
					beforeNormal,
					beforeTimeout,
					pendingNormal,
					pendingTimeout,
					finishCanCancel,
					finishForbidCancel,
				} = this.statusMap;
				return {
					backgroundColor: !item.HasPermission ? this.disabledColor : status === finishForbidCancel || status ===
						finishCanCancel ?
						this.successColor : status === pendingTimeout || status === beforeTimeout ? this.errorColor : status ===
						pendingNormal ? this.$themeCurrent.main : status === beforeNormal ? this.borderColor : ""
				}
			},
			itemClickHandle(...props) {
				if (props[0].HasPermission)
					this.$emit("itemClick", ...props);
			}
		}
	}
</script>

<style lang="scss">
	.hc-order-steps {
		white-space: nowrap;

		&-top {
			display: flex;
			align-items: center;

			&-item {
				position: relative;
				margin: 0 50rpx;
				text-align: center;

				&-before,
				&-after {
					position: absolute;
					top: 28rpx;
					width: 50rpx;
					height: 4rpx;
					background-color: $hc-border-color;
				}

				&-before {
					left: -50rpx;
				}

				&-after {
					right: -50rpx;
				}
			}
		}
	}

	.hc-order-steps_finishForbidCancel {
		height: 60rpx;
		display: flex;
		align-items: center;
		border: 1px solid $hc-type-success;
		border-radius: 8rpx;
		padding: 0 20rpx;
		color: $hc-type-success;
	}

	.hc-order-steps_finishCanCancel {
		height: 60rpx;
		display: flex;
		align-items: center;
		border: 1px solid $hc-type-warning;
		border-radius: 8rpx;
		padding: 0 20rpx;
		color: $hc-white-color;
		background-color: $hc-type-warning;
	}

	.hc-order-steps_pending {
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 20rpx;
		border-radius: 8rpx;
		color: $hc-white-color;
	}

	.hc-order-steps_circle {
		padding: 0 20rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		border: 1px solid $hc-border-color;
		border-radius: 8rpx;
	}

	.hc-order-steps_opacity {
		visibility: hidden;
	}
</style>
