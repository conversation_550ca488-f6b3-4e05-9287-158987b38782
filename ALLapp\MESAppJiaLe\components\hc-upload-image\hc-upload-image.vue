<template>
	<!-- 单个 -->
	<view v-if="isSingleReplace" class="hc-upload-image-item">
		<view @click="selectAndUpload">
			<slot v-if="$slots.default"></slot>
			<hc-image v-else :src="getUrl(sigleRecord)" :imageStyle="imageStyle"></hc-image>
			<view class="hc-upload-image_process" :style="[typeStyle]" v-if="sigleRecord.type === 'loading'">
				<hc-circle-progress :active-color="activeColor" bg-color="transparent" :duration="0" :width="customWidth * 0.6"
				 :border-width="8" :percent="sigleRecord.progress"></hc-circle-progress>
			</view>
		</view>
		<hc-icon v-if="closeable && canOperate && (sigleRecord.url || sigleRecord[attachIdKey])" :color="useIconColor" name="close-circle-fill"
		 @click="sigleCloseHandle" :size="iconSize" class="hc-upload-image_close"></hc-icon>
	</view>
	<!-- 多个 -->
	<view v-else class="hc-upload-image-multiple">
		<view class="hc-upload-image-item" style="margin-right: 20rpx; margin-bottom: 20rpx; font-size: 0;" v-for="(item, index) in fileList"
		 :key="getKey(item, index)">
			<hc-image :src="getUrl(item)" :imageStyle="imageStyle"></hc-image>
			<view class="hc-upload-image_retry" v-if="item.type === 'error'" @tap.stop="retryHandle(index)">点击重试</view>
			<view class="hc-upload-image_process" :style="[typeStyle]" v-if="item.type === 'loading'">
				<hc-circle-progress :active-color="activeColor" bg-color="transparent" :duration="0" :width="customWidth * 0.6"
				 :border-width="8" :percent="item.progress"></hc-circle-progress>
			</view>
			<hc-icon v-if="closeable && canOperate" :color="useIconColor" name="close-circle-fill" @click="closeHandle(index)"
			 :size="iconSize" class="hc-upload-image_close"></hc-icon>
		</view>
		<view class="hc-upload-image-multiple-add" :style="[itemStyle, {color: $themeCurrent.main}]" v-if="canOperate && !isExceed"
		 @click="selectAndUpload">
			<hc-icon name="plus" size="60"></hc-icon>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 搭配typeCode使用 规则返回前禁止操作
				forbidden: true,
				dynamicConfig: null,
				// 上传过程 禁止 删除图片 选择图片
				uploading: false
			};
		},
		props: {
			typeCode: {
				type: String,
			},
			fileList: {
				type: Array,
				default () {
					return []
				}
			},
			customWidth: {
				type: [String, Number],
				default: 140
			},
			customHeight: {
				type: [String, Number],
				default: 140
			},
			nameKey: {
				type: String,
				default: "name",
			},
			attachIdKey: {
				type: String,
				default: "attachId",
			},
			disabled: {
				type: Boolean,
				default: false
			},
			readOnly: {
				type: Boolean,
				default: false
			},
			closeable: {
				type: Boolean,
				default: false
			},
			iconSize: {
				type: Number,
				default: 36
			},
			iconColor: {
				type: String
			}
		},
		computed: {
			activeColor() {
				return this.$themeCurrent.main;
			},
			useIconColor() {
				return this.iconColor || this.$hc.color.error;
			},
			isSingleReplace: function() {
				return this.dynamicConfig && this.dynamicConfig.FMULTIPLE_LIMIT === 1;
			},
			canOperate() {
				return !this.forbidden && !this.disabled && !this.readOnly;
			},
			isExceed() {
				const {
					dynamicConfig,
					fileList
				} = this;
				return dynamicConfig && dynamicConfig.FMULTIPLE_LIMIT > 1 && fileList.length === dynamicConfig.FMULTIPLE_LIMIT;
			},
			itemStyle() {
				return {
					width: this.customWidth + "rpx",
					height: this.customHeight + "rpx"
				}
			},
			typeStyle() {
				return {
					borderRadius: this.dynamicConfig && Number(this.dynamicConfig.FSTYLE) === 3 ? 0 : "50%"
				}
			},
			imageStyle() {
				return Object.assign({}, this.itemStyle, this.typeStyle);
			},
			sigleRecord() {
				let result = {};
				if (this.isSingleReplace) {
					const {
						fileList,
						attachIdKey,
						$hc
					} = this;
					if (fileList.length) {
						result = fileList[0];
					}
				}
				return result;
			}
		},
		created() {
			const {
				typeCode,
				$hc
			} = this;
			if (typeCode) {
				$hc.request({
					url: $hc.api.ADM028AttachmentTypeGetByCodeAsync,
					data: {
						code: typeCode,
					},
					disableLoading: true,
				}).then(res => {
					if (res.StatusCode === 200) {
						const {
							Entity
						} = res;
						this.dynamicConfig = Entity;
						this.forbidden = false;
					}
				});
			}
		},
		methods: {
			retryHandle(index) {
				this.uploadHandle(index, [this.fileList[index]]);
			},
			uploadHandle(startIndex, source) {
				const that = this;
				let errList = [];
				const {
					dynamicConfig,
				} = this;
				const queueHandler = (list) => {
					const next = () => {
						if (list.length) {
							const fn = list.shift();
							fn(next);
						} else {
							if (errList.length) {
								this.$hc.Command.ExceptionDialog({
									content: errList
								});
							}

							this.uploading = false;
						}
					}
					list.shift()(next);
					this.uploading = true;
				}
				queueHandler(source.map((item, index) => (next) => {
					let name;
					// #ifdef H5
					name = item.name;
					// #endif
					// #ifndef H5
					const mh = item.url.match(/(\/\/)(.+)/);
					name = mh && mh[2] ? mh[2] : item.url;
					// #endif
					const uploadTask = uni.uploadFile({
						url: `${this.$hc.requestConfig.baseUrl}${this.$hc.api.apiAttachmentupAttachmentupasync}`,
						filePath: item.url,
						name,
						header: {
							token: this.$hc.Command.GetUUID()
						},
						formData: {
							FTYPE_ID: dynamicConfig.FTYPE_ID,
							FBUCKET_CODE: dynamicConfig.FBUCKET_CODE,
							pcode: dynamicConfig.FPROGRAM_CODE,
							type: dynamicConfig.FTYPE_NAME
						},
						success: (res) => {
							let data;
							try {
								data = JSON.parse(res.data);
							} catch (e) {}
							if (data && data.StatusCode === 200) {
								const {
									Entity
								} = data;
								const {
									FATTACHMENT_ID,
									FATTACHMENT_NAME
								} = Entity[0];
								item.type = "done";
								item.progress = 100;
								item[this.attachIdKey] = FATTACHMENT_ID;
								item[this.nameKey] = FATTACHMENT_NAME;
								const _fileList = [...this.fileList];
								_fileList.splice(startIndex + index, 1, item);
								this.$emit("update:fileList", _fileList);
							} else {
								item.type = "error";
								const _fileList = [...this.fileList];
								_fileList.splice(startIndex + index, 1, item);
								this.$emit("update:fileList", _fileList);
								errList.push(`${name}上传失败，日志：${JSON.stringify(res)}`);
							}
							next();
						},
						fail: err => {
							item.type = "error";
							const _fileList = [...this.fileList];
							_fileList.splice(startIndex + index, 1, item);
							this.$emit("update:fileList", _fileList);
							errList.push(`${name}上传失败，日志：${JSON.stringify(res)}`);
							next();
						}
					});
					uploadTask.onProgressUpdate((res) => {
						item.progress = res.progress;
						this.$emit("update:fileList", [...this.fileList]);
					});
				}));
			},
			getUrl(record) {
				return record.url ? record.url : this.$hc.Command.GetAttachmentURL(record[this.attachIdKey]);
			},
			getKey(item, index) {
				return `${item.url ? item.url : item[this.attachIdKey]}${index}`;
			},
			selectAndUpload() {
				if (!this.$haveAuth) {
					return;
				}
				if (this.uploading) {
					this.uploadingWarn();
					return;
				}
				if (!this.canOperate || this.isExceed) return;
				const {
					dynamicConfig,
					fileList
				} = this;
				const {
					FMULTIPLE_LIMIT
				} = dynamicConfig;
				uni.chooseImage({
					count: FMULTIPLE_LIMIT,
					sourceType: ["album", "camera"],
					sizeType: ["original", "compressed"],
					success: (res) => {
						const {
							tempFiles
						} = res;
						const length = tempFiles.length;
						if (!this.isSingleReplace && fileList.length + length > FMULTIPLE_LIMIT) {
							uni.showToast({
								icon: "none",
								title: `文件个数不能大于${FMULTIPLE_LIMIT}`
							})
						} else if (length) {
							// 微信小程序只支持单文件上传
							const result = tempFiles.map(item => ({
								name: item.name,
								url: item.path,
								progress: 0,
								type: "loading",
							}));
							const len = fileList.length;
							this.$emit("update:fileList", this.isSingleReplace ? result : [...fileList, ...result]);
							this.uploadHandle(this.isSingleReplace ? 0 : len, result);
						}
					}
				});
			},
			uploadingWarn() {
				uni.showToast({
					title: "上传中，请稍等"
				})
			},
			sigleCloseHandle() {
				if (!this.$haveAuth) {
					return;
				}
				if (this.uploading) {
					this.uploadingWarn();
				} else {
					this.$emit("update:fileList", []);
				}
			},
			closeHandle(index) {
				if (!this.$haveAuth) {
					return;
				}
				if (this.uploading) {
					this.uploadingWarn();
				} else {
					const _fileList = [...this.fileList];
					_fileList.splice(index, 1);
					this.$emit("update:fileList", _fileList);
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-upload-image-multiple {
		line-height: 1.5;
	}

	.hc-upload-image-item {
		display: inline-block;
		line-height: 1;
		vertical-align: top;
		position: relative;
	}

	.hc-upload-image_square {
		border-radius: 0;
	}

	.hc-upload-image_close {
		position: absolute;
		top: 4rpx;
		right: 4rpx;
		line-height: 1;
	}

	.hc-upload-image_retry {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		text-align: center;
		font-size: 12px;
		color: $hc-white-color;
		background-color: $hc-type-error;
	}

	.hc-upload-image_process {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
	}

	.hc-upload-image-multiple-add {
		display: inline-flex;
		justify-content: center;
		align-items: center;
		background-color: $hc-bg-color;
		border-radius: 12rpx;
	}
</style>
