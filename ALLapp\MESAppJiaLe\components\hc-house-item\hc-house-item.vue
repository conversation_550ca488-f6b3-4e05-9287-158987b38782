﻿<template>
	<view class="item" v-if="item" :key="index">
		<view class="item-left">
			<hc-image :src="$hc.Command.GetAttachmentURL(item.FATTCHMENT_ID)"></hc-image>
		</view>
		<view style="flex: 1;" class="item-content">
			<view class="item-content-title pro-desc">{{ item.FHOUSE_NAME||"" }}</view>
			<view class="item-content-type pro-desc">地区：{{ item.FDIVISION_NAME||"" }}</view>
			<view class="item-content-type pro-desc">详细地址：{{ item.FADDRESS||"" }}</view>
			<!-- <hc-row style="padding-bottom: 20rpx;">
				<hc-col :span="12">
					<view class="item-content-title pro-desc">{{ item.FHOUSE_NAME||"" }}</view>
				</hc-col>
			</hc-row>
			<hc-row style="padding-bottom: 20rpx;">
				<hc-col :span="12">
					<view class="item-content-type pro-desc">{{ item.FDIVISION_NAME||"" }}</view>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="12">
					<view class="item-content-type pro-desc">{{ item.FADDRESS||"" }}</view>
				</hc-col>
			</hc-row> -->
		</view>
	</view>
</template>

<script>
	export default {
		name: "hc-house-item",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {

			};
		},
		methods: {}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		// border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 160rpx;
			height: 160rpx;
		}

		&-content {
			flex:1;
			display: flex;
			flex-direction:column;
			
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				font-weight: bold;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
			}
		}


	}

	.pro-desc {
		width: 500rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
