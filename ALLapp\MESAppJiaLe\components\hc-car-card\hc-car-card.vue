<template>
	<view class="card-out-box" v-if="item" :key="index" >
		<view>
			<hc-row>
				<hc-col :span="2.5" class="card-title" >
					<label >
						<text>车辆型号:</text>
					</label>
				</hc-col>
				<hc-col :span="3" class="card-value">
					<label >
						<text>{{item.FCAR_SPEC}}</text>
					</label>
				</hc-col>
				<hc-col :span="2.5" class="card-title">
					<label >
						<text>司机:</text>
					</label>
				</hc-col>
				<hc-col :span="3.5" class="card-value">
					<label >
						<text>{{item.FDRIVER}}</text>
					</label>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="2.5" class="card-title">
					<label >
						<text>车牌号:</text>
					</label>
				</hc-col>
				<hc-col :span="3" class="card-value">
					<label >
						<text>{{item.FCARD_NO}}</text>
					</label>
				</hc-col>
				<hc-col :span="2.5" class="card-title">
					<label >
						<text>电话号码:</text>
					</label>
				</hc-col>
				<hc-col :span="3.5" class="card-value">
					<label >
						<text>{{item.FDRIVER_TEL}}</text>
					</label>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="2.5" class="card-title">
					<label>
						<text>手机号:</text>
					</label>
				</hc-col>
				<hc-col :span="3.5" class="card-value">
					<label>
						<text>{{item.FDRIVER_MOBILE}}</text>
					</label>
				</hc-col>
				<hc-col :span="6"></hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	export default {
		name:"hc-car-card",
		props:{
			read: {
				type: Boolean,
				default: true
			},
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss">
	.card-title{
		text-align: right;
		padding-bottom: 10rpx;
	}
	.card-value{
		text-align: left;
		padding-bottom: 10rpx;
	}
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;

		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				text-align: right;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}

		.item-content {

			text-indent: 0;
		}
	}

	.Focus {
		color: red;
		font-weight: 900;
	}

	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.card-out-box {
		margin: 15rpx 20rpx 15rpx 20rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 15rpx 0;
	}

	.big-title {
		// font-size: 30rpx;
		font-weight: bold;
	}

</style>

