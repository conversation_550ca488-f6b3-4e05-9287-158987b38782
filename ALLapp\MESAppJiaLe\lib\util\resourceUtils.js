import requestConfig from '../../request/config.js';
import request from '../../request/request.js';
import api from '../../request/api.js';
import {
	IsEmpty,
	GetAttachmentURL
} from '@/lib/util/command.js';
import {
	SetSysUIResource,
	GetSysUIResource
} from './storage.js';

export default {

};

/**
 * 请求系统界面资源
 * **/
export function RequestSysUIResource() {
	return request.request({
		disableLoading: true,
		url: api.GetSysUIResource,
		data: {},
	}).then(res => {
		if (res.StatusCode == 200) {
			SetSysUIResource(res.Entity);
		}
	}).catch(err => {});
};

/**
 * 获取未读消息 数
 */
export function GetNotRead() {
	return request.request({
		disableLoading: true,
		url: "/api/ADM008Message/GenAlertCountAsync",
		data: {},
	}).then(res => {
		if (res.StatusCode == 200) {
			if (res.Entity) {
				const {
					MESSAGE_COUNT,
					WARN_COUNT
				} = res.Entity
				const num = MESSAGE_COUNT + WARN_COUNT;
				if (num > 0) {
					uni.showTabBarRedDot({ //显示红点
						index: 1
					})
				} else {
					uni.hideTabBarRedDot({ //隐藏红点
						index: 1
					})
				}
			}

		}
	}).catch(err => {});


}


/**
 * 根据key返回系统界面资源
 * @param {any} reourceData
 */
export function GetSysUIResourceByKey(key) {

	var result = null;
	try {
		var resource = GetSysUIResource();
		if (!IsEmpty(resource)) {
			var val = resource[key];

			if (val.IsAttach) {
				result = GetAttachmentURL(val.Value);
			} else {
				result = val.Value;
			}
		}
	} catch (error) {
		console.error(error);

	}
	return result;
};

/**
 * 返回启动页背景图
 * **/
export function GetStartBackImage() {
	var startBackImage = GetSysUIResourceByKey("StartBackImage");
	if (IsEmpty(startBackImage)) {
		if (requestConfig.sysProduct == "MES") {
			startBackImage = "/static/images/start.png";
		}
	}
	return startBackImage;
};

/**
 * 返回Logo1
 * **/
export function GetLogo1() {
	var Logo1 = GetSysUIResourceByKey("Logo1");
	if (IsEmpty(Logo1)) {
		if (requestConfig.sysProduct == "MES") {
			Logo1 = "/static/images/logo_transparent.png";
		}
	}
	return Logo1;
	// return Logo1;
};

/**
 * 返回Logo2
 * **/
export function GetLogo2() {
	var Logo2 = GetSysUIResourceByKey("Logo2");
	if (IsEmpty(Logo2)) {
		if (requestConfig.sysProduct == "MES") {
			Logo2 = "/static/images/propaganda.png";
		}
	}
	return Logo2;
};

/**
 * 返回系统简称
 * **/
export function GetSysName() {
	var SysName = GetSysUIResourceByKey("SysName");
	if (IsEmpty(SysName)) {
		if (requestConfig.sysProduct == "MES") {
			SysName = "HCloud·MES";
		}
	}
	return SysName;
};
