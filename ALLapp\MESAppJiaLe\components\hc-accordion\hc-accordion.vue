﻿<template>
	<view class="hc-accordion">
		<accordion-item v-for="item in source" :key="item[nodeKey]" :record="item" :itemHeight="itemHeight"></accordion-item>
	</view>
</template>

<script>
	import accordionItem from './accordion-item.vue';
	/**
	 * accordion 手风琴 无限级
	 * @description 可进行 节点 展开收起 选择
	 * @property {Array} selected 选中的节点
	 * @property {Array} source 数据源
	 * @property {String} titleKey title属性名
	 * @property {String} nodeKey 唯一键属性名
	 * @property {String} childrenKey children属性名
	 * @event {Function} itemClick 某个节点被点击时触发
	 * @example <hc-accordion :source="source" :selected="selected" @itemClick="itemClickHandle"></hc-accordion>
	 */
	export default {
		components: {
			accordionItem
		},
		provide() {
			return {
				accordion: this
			}
		},
		props: {
			selected: {
				type: Array,
				default () {
					return []
				}
			},
			source: {
				type: Array,
				default () {
					return []
				}
			},
			titleKey: {
				type: String,
				default: "title"
			},
			nodeKey: {
				type: String,
				default: "id"
			},
			childrenKey: {
				type: String,
				default: "children"
			},
			itemHeight: {
				type: Number,
				default: 70
			}
		},
		data() {
			return {

			};
		},
		methods: {
			clickHandle(record) {
				this.$emit("itemClick", record);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-accordion {
		border-top: 1px solid $uni-border-color;
	}
</style>
