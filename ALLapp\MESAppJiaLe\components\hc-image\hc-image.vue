﻿<template>
	<image :style="[imageStyle]" :src="userSrc" :mode="mode" :lazyLoad="lazyLoad" :fadeShow="fadeShow" :webp="webp"
	 :showMenuByLongpress="showMenuByLongpress" @error="errorHandle" @load="loadHandle" @tap="tapHandle" />
</template>

<script>
	import emptyPng from '@/static/images/empty.png';
	/**
	 * image 图片
	 * @description 增强image基础组件 图片加载失败显示空图
	 * @property {Object} imageStyle 样式  默认 width: 100% height: 100%
	 * @property {String} src 图片url
	 * @property {String} mode 图片裁剪、缩放的模式 默认 scaleToFill
	 * @property {Boolean} lazyLoad 图片懒加载。只针对page与scroll-view下的image有效
	 * @property {Boolean} fadeShow 图片显示动画效果
	 * @property {Boolean} webp 默认不解析 webP 格式，只支持网络资源
	 * @property {Boolean} showMenuByLongpress 开启长按图片显示识别小程序码菜单
	 * @property {Boolean} preview 点击预览
	 * @example <hc-image src="" imageStyle=""></hc-image>
	 */
	export default {
		data() {
			return {
				userSrc: this.src || emptyPng
			};
		},
		props: {
			imageStyle: {
				type: Object,
				default () {
					return {
						width: "100%",
						height: "100%",
					}
				}
			},
			src: {
				type: String,
				default: ""
			},
			mode: {
				type: String,
				default: "scaleToFill"
			},
			lazyLoad: {
				type: Boolean,
				default: false
			},
			fadeShow: {
				type: Boolean,
				default: true
			},
			webp: {
				type: Boolean,
				default: false
			},
			showMenuByLongpress: {
				type: Boolean,
				default: false
			},
			preview: {
				type: Boolean,
				default: false
			},
			previewUrls: {
				type: Array,
				default () {
					return []
				}
			}
		},
		watch: {
			src(newVal) {
				this.userSrc = newVal || emptyPng;
			}
		},
		methods: {
			errorHandle(event) {
				this.userSrc = emptyPng;
				this.$emit("load", event);
			},
			loadHandle(event) {
				this.$emit("load", event);
			},
			tapHandle() {
				if (this.preview) {
					if (!this.previewUrls ||
						this.previewUrls.length <= 0) {
						this.previewUrls = [this.userSrc]
					}
					uni.previewImage({
						current: this.userSrc,
						urls: this.previewUrls
					});
				}
				this.$emit("click");
			}
		}
	}
</script>

<style lang="scss">

</style>
