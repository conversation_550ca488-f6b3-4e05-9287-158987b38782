# 出库订单卡片组件 (hc-out-order-card)

## 概述

这是一个用于委外出库操作的Vue组件，支持物料清单管理和标签登记功能。该组件已经过全面优化，提高了代码质量、可维护性和性能。

## 主要功能

- 📦 物料清单管理
- 🏷️ 标签登记和管理
- 📱 扫码输入支持
- 🏪 仓库和货位选择
- 📊 实时数据更新

## 组件结构

```
hc-out-order-card/
├── hc-out-order-card.vue      # 主组件
├── material-info-display.vue  # 物料信息显示组件
├── material-item-card.vue     # 物料项目卡片组件
├── tag-item-card.vue         # 标签项目卡片组件
├── test-page.vue             # 测试页面
└── README.md                 # 说明文档
```

## 使用方法

### 基本用法

```vue
<template>
  <hc-out-order-card 
    :edit-data="editData"
    :index="0"
  />
</template>

<script>
import HcOutOrderCard from '@/components/hc-out-order-card/hc-out-order-card.vue'

export default {
  components: {
    HcOutOrderCard
  },
  data() {
    return {
      editData: {
        title: "出库数",
        field: "FNUM",
        toptitle: "委外出库",
        billtype: "OutOrderOutScan",
        searchtitle: "委外单号"
      }
    }
  }
}
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| index | Number | 0 | 组件索引 |
| editData | Object | 见下方 | 编辑配置数据 |

#### editData 对象结构

```javascript
{
  title: "出库数",           // 操作类型标题
  field: "FNUM",            // 字段名
  toptitle: "委外出库",      // 页面标题
  billtype: "OutOrderOutScan", // 单据类型
  searchtitle: "委外单号"    // 搜索框标题
}
```

## 优化内容

### 1. 代码结构优化

- ✅ 消除了大量重复代码
- ✅ 提取了可复用的子组件
- ✅ 改进了组件层次结构
- ✅ 添加了详细的代码注释

### 2. 性能优化

- ✅ 使用计算属性优化数据处理
- ✅ 减少了不必要的DOM操作
- ✅ 优化了事件处理逻辑
- ✅ 改进了数据绑定方式

### 3. 可维护性提升

- ✅ 统一了代码风格和命名规范
- ✅ 分离了业务逻辑和视图逻辑
- ✅ 提供了清晰的组件文档
- ✅ 添加了错误处理机制

### 4. 样式优化

- ✅ 重构了CSS样式结构
- ✅ 添加了响应式设计支持
- ✅ 统一了样式变量和主题
- ✅ 优化了布局和间距

## API 接口

组件使用以下API接口：

- `GET /api/MES017Tag/GeneratePredictBomCodeList` - 获取出入库清单
- `GET /api/STK001Store/GeStoreAndPlaceAsync` - 获取仓库和货位信息
- `POST /api/MES017Tag/GenrateOrderInsertAsync` - 执行入库操作

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| click | 物料项目点击 | (item, index) |
| add-tag | 添加标签 | (item) |
| remove-tag | 删除标签 | (item, index) |
| store-click | 仓库选择点击 | (item, index) |

## 测试

运行测试页面：

```vue
<test-page />
```

测试包括：
- 组件渲染测试
- Props传递测试
- 基本功能测试

## 注意事项

1. 确保已正确配置API接口地址
2. 组件依赖于全局的 `$hc` 对象
3. 需要引入相关的UI组件库
4. 建议在使用前进行充分测试

## 更新日志

### v2.0.0 (优化版本)
- 重构了整体架构
- 提取了子组件
- 优化了性能和样式
- 添加了完整文档

### v1.0.0 (原始版本)
- 基础功能实现
