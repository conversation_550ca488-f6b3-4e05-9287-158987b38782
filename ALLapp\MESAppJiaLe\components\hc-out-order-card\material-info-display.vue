<template>
	<view class="material-info">
		<hc-row>
			<hc-col :span="12">
				<view class="info-item">
					<text class="label">名称：</text>
					<text class="value">{{ item.FMATERIAL_NAME || "" }}</text>
				</view>
			</hc-col>
		</hc-row>
		
		<hc-row>
			<hc-col :span="6">
				<view class="info-item">
					<text class="label">编号：</text>
					<text class="value">{{ item.FMATERIAL_CODE || "" }}</text>
				</view>
			</hc-col>
			<hc-col :span="6">
				<view class="info-item">
					<text class="label">单位：</text>
					<text class="value">{{ item.FSTK_UNIT_NAME || "" }}</text>
				</view>
			</hc-col>
		</hc-row>
		
		<hc-row>
			<hc-col :span="6">
				<view class="info-item">
					<text class="label">已{{ editData.title }}：</text>
					<text class="value">{{ item.FSCAN_NUM || 0 }}</text>
				</view>
			</hc-col>
			<hc-col :span="6">
				<view class="info-item">
					<text class="label">{{ showTotal ? '总数' : editData.title }}：</text>
					<text class="value">{{ item.FNUM || 0 }}</text>
				</view>
			</hc-col>
		</hc-row>
	</view>
</template>

<script>
/**
 * 物料信息显示组件
 * 用于显示物料的基本信息
 */
export default {
	name: "material-info-display",
	
	props: {
		item: {
			type: Object,
			required: true
		},
		editData: {
			type: Object,
			required: true
		},
		showTotal: {
			type: Boolean,
			default: false
		}
	}
}
</script>

<style lang="scss" scoped>
.material-info {
	width: 100%;
	
	.info-item {
		display: flex;
		align-items: center;
		margin: 8rpx 0;
		font-size: 26rpx;
		
		.label {
			color: #666;
			margin-right: 8rpx;
		}
		
		.value {
			color: #333;
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}
</style>
