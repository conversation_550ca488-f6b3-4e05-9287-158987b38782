<template>
	<view class="card-out-box" v-if="item" :key="index" @tap="Tap">
		<!-- <hc-read-more :toggle="read" style="width: 100%;text-indent: 0;" show-height="300" > -->
		<view>
			<hc-row>
				<hc-col :span="12">
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label class="big-title">
								<text>{{model.FWORK_ORDER_NO}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label class="big-title">
								<text>{{item.FWORK_ORDER_NO}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSALE_ORDER_NO}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FSALE_ORDER_NO}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FMATERIAL_NAME}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FMATERIAL_NAME}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FMATERIAL_CODE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FMATERIAL_CODE}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FPRO_QTY}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FPRO_QTY}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FPRO_UNIT_NAME}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FPRO_UNIT_NAME}}</text>
							</label>
						</hc-col>
					</hc-row>

					<!-- 		<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FCREATOR}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FCREATOR}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FPLAN_ST_DATE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FPLAN_ST_DATE?item.FPLAN_ST_DATE.split(' ')[0]:''}}</text>
							</label>
						</hc-col>
					</hc-row> -->

					<!-- <hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSEND_DATE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FSEND_DATE?item.FSEND_DATE.split(' ')[0]:''}}</text>
							</label>
						</hc-col>
					</hc-row> -->
				</hc-col>
			</hc-row>
		</view>
		<!-- </hc-read-more> -->
	</view>
</template>

<script>
	export default {
		name: "hc-workOrder-card",
		props: {
			read: {
				type: Boolean,
				default: true
			},
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				// modelList: [{
				// 		Name: "工单编号",
				// 		Key: "FWORK_ORDER_NO",
				// 	},
				// 	{
				// 		Name: "排程任务编号",
				// 		Key: "FCRAFT_SCHEDULE_NO",
				// 	},
				// 	{
				// 		Name: "加工任务编号",
				// 		Key: "FCRAFT_JOB_BOOKING_NO",
				// 	},
				// 	{
				// 		Name: "产品",
				// 		Key: "FMATERIAL",
				// 	},
				// 	{
				// 		Name: "工艺",
				// 		Key: "FCRAFT",
				// 	},
				// 	{
				// 		Name: "工位",
				// 		Key: "FSTATION",
				// 	},
				// 	{
				// 		Name: "报工人员",
				// 		Key: "FEMP",
				// 	},
				// 	{
				// 		Name: "实际开工时间",
				// 		Key: "FACT_ST_DATE",
				// 	},
				// 	{
				// 		Name: "实际完工时间",
				// 		Key: "FACT_ED_DATE",
				// 	},
				// 	{
				// 		Name: "实际工时(小时)",
				// 		Key: "FACT_USE_HOUR",
				// 	},
				// 	// {
				// 	// 	Name:"合格数量",
				// 	// 	Key:"FPASS_QTY",
				// 	// },
				// 	// {
				// 	// 	Name:"合格重量",
				// 	// 	Key:"FPASS_WEIGHT",
				// 	// },
				// 	// {
				// 	// 	Name:"不良数量",
				// 	// 	Key:"FNG_QTY",
				// 	// },
				// 	// {
				// 	// 	Name:"不良重量",
				// 	// 	Key:"FNG_WEIGHT",
				// 	// },
				// ],
				model: {

					FWORK_ORDER_NO: "生产工单编号",
					FSALE_ORDER_NO: "订单编号",
					FMATERIAL_NAME: "产品名称",
					FMATERIAL_CODE: "产品编号",
					FPRO_QTY: "生产数量",
					FPRO_UNIT_NAME: "生产单位",
					FCREATOR: "建立人",
					FPLAN_ST_DATE: "计划开始日期",

				}
			};
		},
		methods: {
			Tap() {
				this.$emit("click", this.item)
			},
		}
	}
</script>
<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;

		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				text-align: right;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}

		.item-content {

			text-indent: 0;
		}
	}

	.Focus {
		color: red;
		font-weight: 900;
	}

	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.card-out-box {
		margin: 15rpx 20rpx 15rpx 20rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 15rpx 0;
	}

	.big-title {
		font-size: 30rpx;
		font-weight: bold;
	}
</style>
