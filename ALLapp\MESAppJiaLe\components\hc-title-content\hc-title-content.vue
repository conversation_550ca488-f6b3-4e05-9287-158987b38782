<template>
	<view class="hc-title-content">
		<view class="hc-title-content-title">
			<view class="hc-title-content-title-bar" :style="{backgroundColor: $themeCurrent.main}"></view>
			<text>{{title}}</text>
			<view class="title-right">
				<slot name="title-right"></slot>
			</view>
		</view>
		<view class="hc-title-content-content" :class="[contentBigSpace?'hc-title-content-bigSpaceContent':'']">
			<slot></slot>
		</view>
	</view>
</template>

<script>
	/**
	 * hc-title-content 标题内容
	 * @description 适用于上部分为标题下部分为内容
	 * @property {String} title 标题名称
	 * @example <hc-title-content title="标题"><view>内容</view></hc-title-content>
	 */
	export default {
		name: 'hc-title-content',
		data() {
			return {

			};
		},
		props: {
			title: String,
			//是否大间隔宽度
			contentBigSpace: {
				type: Boolean,
				default: false
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-title-content {
		padding: 20rpx;
		background-color: #fff;

		&-title {
			display: flex;
			align-items: center;

			&-bar {
				width: 4px;
				height: 30rpx;
				background-color: $hc-type-primary;
				margin-right: 20rpx;
				margin-left: 25rpx;
				border-radius: 5rpx;
			}

			font-size: 30rpx;
			font-weight: 700;

			& .title-right {
				position: absolute;
				right: 0px;
			}
		}

		&-content {
			margin-top: 20rpx;
		}

		&-bigSpaceContent {
			margin-top: 30rpx;
		}
	}
</style>
