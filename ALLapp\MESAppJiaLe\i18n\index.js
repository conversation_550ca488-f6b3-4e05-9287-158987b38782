﻿import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zhCN from './locale/zh-CN.js'
import en from './locale/en.js'
import {
	GetUserLang
} from '../lib/util/storage.js'
import {
	GetSupportLangs
} from '../lib/util/command.js'
import dayjs from "dayjs";

Vue.use(VueI18n)

const fallbackLocale = "CN";
let {
	FRES_LANGTYPE_CODE
} = GetUserLang() || {};

const supportLangs = GetSupportLangs();
if (!supportLangs[FRES_LANGTYPE_CODE]) {
	FRES_LANGTYPE_CODE = fallbackLocale;
}
const info = supportLangs[FRES_LANGTYPE_CODE];
dayjs.locale(info.locale);

const i18n = new VueI18n({
	locale: FRES_LANGTYPE_CODE,
	fallbackLocale,
	messages: {
		CN: zhCN,
		EN: en,
	}
})

export default i18n
