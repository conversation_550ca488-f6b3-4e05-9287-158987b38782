﻿<template>
	<view class="hc-panel-step">
		<view class="hc-panel-step-left">
			<slot name="icon"></slot>
		</view>
		<view class="hc-panel-step-right">
			<slot name="content"></slot>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss" scoped>
	.hc-panel-step {
		display: flex;

		&-left {
			display: flex;
			flex-direction: column;
			align-items: center;

			&::after {
				content: "";
				width: 2rpx;
				background-color: $hc-border-color;
				flex: 1;
				margin-top: 4rpx;
			}
		}

		&-right {
			flex: 1;
		}
	}
</style>
