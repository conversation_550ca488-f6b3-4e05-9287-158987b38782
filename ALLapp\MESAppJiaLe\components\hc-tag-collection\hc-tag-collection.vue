﻿<template>
	<view class="hc-tag-collection">
		<view class="row">
			<view v-for="(item,index) in source" :key="index" style="margin-bottom: 14rpx;" :style="{
				padding: `0 ${Number(gutter)/2 + 'rpx'}`,
				flex: `0 0 ${100 / 12 * span}%`
			}">
				<view @click="tagClick(item,extraIndex)" class="node" :class="[
						value.indexOf(getItemKey(item))>=0?'node-select':''
					]"
				 :style="{backgroundColor: value.indexOf(getItemKey(item))>=0 ? $themeCurrent.main : null}">
					<view style="width: 28rpx;"><hc-icon class="node-select-icon" name="checkmark-circle"></hc-icon></view>
					{{item[descField]}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * radioRroup 标签集合组件
	 * @description 标签集合组件，可多选标签
	 * @property {Boolean} source 数据源
	 * @property {Number} col 一行多少个标签，12等分分配法
	 * @property {String,Function} idField id字段，支持函数自定义id
	 * @property {String} descField 描述字段
	 * @event {Function} click 点击标签时触发
	 * @property {Boolean} isAutoBind 是否自动绑定v-model。与click一起使用，可选择使用手工绑定值
	 * @example <hc-tag-collection v-model="Filter.CheckNodes" 
				 :source="Filter.FilterNodes" 
				 idField="ColumnName" descField="Caption"
				 :col="3" @click="nodeClick" >
				</hc-tag-collection>
				
				<hc-tag-collection v-model="CheckNodes" :source="Nodes" descField="Caption"
						:col="3" @click="SelectNode" :idField="custIdField">
				</hc-tag-collection>
				custIdField(item){
					return item.Nodes[0].Id;
				},
	 */
	export default {
		name: 'hc-tag-collection',
		props: {
			//数据源
			source: {
				type: Array,
				default () {
					return [];
				}
			},
			//v-model
			value: {
				type: Array,
				default () {
					return [];
				}
			},
			//一行多少个标签，一行为12等分
			col: {
				type: Number,
				default: 3
			},
			//id字段
			idField: {
				type: [String, Function],
				default: undefined
			},
			//描述字段
			descField: {
				type: String,
				default: undefined
			},
			gutter: {
				type: Number,
				default: 20
			},
			//是否自动绑定v-model
			isAutoBind: {
				type: Boolean,
				default: true
			},
			//数据源索引
			extraIndex: {
				type: Number,
				default: 0
			}
		},
		created() {},
		data() {
			return {};
		},
		computed: {
			span() {
				return 12 / this.col;
			}
		},
		methods: {
			getItemKey(item) {
				if (typeof this.idField == "function") {
					return this.idField(item);
				}
				return item[this.idField];
			},
			tagClick(item, extraIndex) {
				if (this.isAutoBind) {
					let key = this.getItemKey(item);
					let index = this.value.indexOf(key)
					if (index >= 0) {
						this.value.splice(index, 1);
					} else {
						this.value.push(key);
					}
					//触发v-model绑定事件
					this.$emit('input', this.value);
				}
				this.$emit('click', item, extraIndex);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.hc-tag-collection {
		padding: 0rpx 14rpx;

		& .row {
			flex-wrap: wrap;
			display: flex;
		}

		& .node {
			height: 100%;
			display: flex;
			flex-wrap: nowrap;
			justify-content: center;
			align-items: center;
			background-color: $hc-bg-color;
			color: $hc-content-color;
			text-align: center;
			padding: 16rpx 28rpx 16rpx 5rpx;
			border-radius: 8rpx;
			word-break: break-all;
			// height: 60rpx;
			// line-height: 60rpx;
			// margin: 14rpx 0rpx;
		}

		& .node-select {
			color: $hc-white-color;

			&-icon {
				display: none;
				padding-right: 4rpx;
			}

			& .node-select-icon {
				color: $hc-white-color;
				display: inline-block;
			}

		}
	}
</style>
