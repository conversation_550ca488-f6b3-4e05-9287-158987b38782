﻿<template>
	<view class="hc-calenders">
		<view class="hc-calenders-top">
			<view>
				<hc-button-group>
					<hc-button :type="useMode === 'month' ? 'primary' : 'default'" size="mini" @click="toggleMode('month')" isGroup
					 isGroupFirst shape="square">月</hc-button>
					<hc-button :type="useMode === 'week' ? 'primary' : 'default'" size="mini" @click="toggleMode('week')" isGroup
					 isGroupLast shape="square">周</hc-button>
				</hc-button-group>
			</view>
			<view class="hc-calenders-top-center">
				<hc-icon name="arrow-left" @click="prev"></hc-icon>
				<text style="margin: 0 20rpx;">{{currentDateStr}}</text>
				<hc-icon name="arrow-right" @click="next"></hc-icon>
			</view>
			<view>
				<slot name="right"></slot>
			</view>
		</view>
		<view class="hc-calenders-header">
			<view v-for="item in headerList" :key="item" class="hc-calenders-header-item">{{item}}</view>
		</view>
		<view class="hc-calenders-content">
			<view v-for="item in days" :key="item.valueOf()" class="hc-calenders-content-item" @tap="itemTapHandle(item.valueOf())">
				<view :style="{backgroundColor: $hc.moment(useSelectDate).format('YY-M-D') === item.format('YY-M-D') ? $themeCurrent.main : null}"
				 :class="['hc-calenders-content-item-box', {active: $hc.moment(useSelectDate).format('YY-M-D') === item.format('YY-M-D')}]">
					{{item.date()}}
					<text class="hc-calenders-content-item-box-tip" v-if="getTip(item.valueOf())">{{getTip(item.valueOf())}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				current: this.$hc.moment(),
				useSelectDate: null,
				headerList: ["日", "一", "二", "三", "四", "五", "六"],
				days: [],
				useMode: this.mode,
			};
		},
		props: {
			// 模式 month week
			mode: {
				type: String,
				default: "month"
			},
			// 日期节点信息
			selected: {
				type: Array,
				default () {
					return []
				}
			},
			// 选中的日期节点 兼容微信小程序 不能传Object
			selectDate: {
				type: [String, Number]
			}
		},
		watch: {
			selectDate: {
				handler(newVal) {
					this.useSelectDate = this.$hc.moment(newVal);
				},
				immediate: true
			},
			mode: {
				handler(newVal) {
					const {
						$hc
					} = this;
					
					this.useMode = newVal;
					let result = [];
					if (newVal === "week") {
						result = this.getDays(this.useSelectDate);
					} else {
						if (this.useSelectDate) {
							this.current = $hc.moment(this.useSelectDate);
						}
						result = this.getDays(this.current.date(1));
					}
					this.days = result;
					this.$emit("modeChange", newVal, result);
				},
				immediate: true
			},
			selected() {
				this.$emit('randerStart', '')		
				this.$nextTick(() => {
					this.$emit('randerComplete', '')
				});
			},
		},
		methods: {
			itemTapHandle(timestamp) { // 兼容微信小程序 传入date参数为时间戳
				const {
					selected,
					$hc
				} = this;
				const date = $hc.moment(timestamp);
				this.useSelectDate = date;
				let record;
				record = selected.find(item => $hc.moment(item.date).format("YY-M-D") === date.format("YY-M-D"));
				this.$emit("change", date, record);
			},
			getTip(timestamp) { // 兼容微信小程序 传入date参数为时间戳
				const {
					selected,
					$hc
				} = this;
				const date = $hc.moment(timestamp);
				if (selected.length) {
					const record = selected.find(item => $hc.moment(item.date).format("YY-M-D") === date.format("YY-M-D"));
					if (record) {
						return String(record.pointText);
					}
				}
			},
			toggleMode(mode) {
				const {
					$hc
				} = this;
				if(this.useMode == mode){
					return;
				}
				this.useMode = mode;
				let result = [];
				if (mode === "week") {
					result = this.getDays(this.useSelectDate);
				} else {
					if (this.useSelectDate) {
						this.current = $hc.moment(this.useSelectDate);
					}
					result = this.getDays(this.current.date(1));
				}
				this.days = result;
				this.$emit("modeChange", mode, result);
			},
			prev() {
				const {
					current,
					days
				} = this;
				if (this.useMode === "week") { // 周模式
					const end = days[0];
					this.days = this.getDays(end.subtract(1, "day"));
					this.$emit("weekSwitch", this.days, "prev");
				} else { // 月模式
					this.current = current.subtract(1, "month");
					this.days = this.getDays(this.current.date(1));
					this.$emit("monthSwitch", this.days, "prev", this.current.format("YYYY-MM"));
				}
			},
			next() {
				const {
					current,
					days
				} = this;
				if (this.useMode === "week") { // 周模式
					const start = days.slice(-1)[0];
					this.days = this.getDays(start.add(1, "day"));
					this.$emit("weekSwitch", this.days, "next");
				} else { // 月模式
					this.current = current.add(1, "month");
					this.days = this.getDays(this.current.date(1));
					this.$emit("monthSwitch", this.days, "next", this.current.format("YYYY-MM"));
				}
			},
			getDays(date) {
				
				const start = this.$hc.moment(date);
				let result = [];
				// 星期几 0-6
				const currentWeek = start.day();

				// 小于等于当前星期
				for (let i = currentWeek; i >= 0; i -= 1) {
					result.push(start.set('date', start.date() - i));
				}
				// 大于当前星期
				for (let m = 1; m < this.length - currentWeek; m += 1) {
					result.push(start.set('date', start.date() + m));
				}
				return result;
			}
		},
		computed: {
			currentDateStr() {
				const {
					current,
					useMode,
					days
				} = this;
				if (useMode === "week") {
					return `${days[0].format("YY/MM/DD")}-${days.slice(-1)[0].format("YY/MM/DD")}`;
				} else {
					return current.format("YYYY-MM");
				}
			},
			length() {
				return this.useMode === "week" ? 7 : 42;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-calenders {
		&-top {
			height: 80rpx;
			display: flex;
			align-items: center;
			padding: 0 10rpx;

			&-center {
				flex: 1;
				display: flex;
				justify-content: center;
				font-size: 36rpx;
			}
		}

		&-header {
			display: flex;
			align-items: center;
			height: 60rpx;
			border-top: 1px solid $hc-border-color;
			border-bottom: 1px solid $hc-border-color;
			font-size: 24rpx;

			&-item {
				flex: 1;
				text-align: center;
			}
		}

		&-content {
			display: flex;
			flex-wrap: wrap;
			background-color: $hc-white-color;

			&-item {
				flex-basis: 14.285%;
				padding: 10rpx 0;
				display: flex;
				justify-content: center;
				align-items: center;

				&-box {
					position: relative;
					width: 80rpx;
					height: 80rpx;
					line-height: 80rpx;
					text-align: center;
					border-radius: 50%;

					&-tip {
						position: absolute;
						top: -8rpx;
						right: -8rpx;
						width: 40rpx;
						height: 40rpx;
						line-height: 40rpx;
						text-align: center;
						border-radius: 50%;
						font-size: 24rpx;
						background-color: $hc-type-error;
						color: $hc-white-color;
					}
				}

				.active {
					color: $hc-white-color;
				}
			}
		}
	}
</style>
