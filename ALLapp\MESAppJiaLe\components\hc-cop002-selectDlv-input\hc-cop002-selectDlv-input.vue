<template>
	<view class="outBox">
		<view class="item" v-if="item" :key="index">
			<view style="margin: 10rpx;overflow: auto;">
				<hc-form style="border-radius: 20rpx;">
					<hc-form-item label="订单编号" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FSALE_ORDER_NO" input-align="right" maxlength="50"
							placeholder="" ref="FSALE_ORDER_NO" disabled :clearable="false">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="产品名称" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FMATERIAL_NAME" input-align="right" maxlength="50"
							placeholder="" ref="FMATERIAL_NAME" disabled :clearable="false">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="产品规格" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FSPEC_DESC" input-align="right" maxlength="50"
							placeholder="" ref="FSPEC_DESC" disabled :clearable="false">
						</hc-input>
					</hc-form-item>
					<hc-form-item label="出库数量" label-width="auto" prop="">
						<hc-col :span="10">
							<hc-input type="number" v-model="item.FSALE_UNIT_QTY" input-align="right" maxlength="50"
								placeholder="请输入出库数量" ref="FSALE_UNIT_QTY" :clearable="false" dataColor="warn">
							</hc-input>
						</hc-col>
						<hc-col :span="2" style="text-align: right;">
							{{item.FSALE_UNIT_NAME}}
						</hc-col>
					</hc-form-item>
					<hc-form-item label="仓库" label-width="auto" prop="">
						<hc-input type="text" v-model="item.FSTORE_NAME" input-align="right" maxlength="50"
							placeholder="请选择仓库" ref="FSTORE_NAME" disabled :clearable="false"
							@tap="changeSelect(item,index,'FSTORE_NAME','FSTORE_ID')">
						</hc-input>
					</hc-form-item>
				</hc-form>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "hc-cop002-selectDlv-input",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
				},
			};
		},
		computed: {

		},
		methods: {
			//选择下拉
			changeSelect(item, index, name, id) {
				this.$emit("updateSelect", {
					name,
					id,
					item,
					index
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.outBox {
		background-color: #eef1f5;

		.item {
			margin-top: 5rpx;
			margin-left: 5rpx;
			margin-right: 5rpx;
		}
	}
</style>
