<template>
	<view class="hc-icon-text" @tap="tapHandle">
		<view class="hc-icon-text-icon" :style="[iconStyle]">
			<hc-icon :icon="icon" :size="60"></hc-icon>
			<slot name="superscriptIcon"></slot>
		</view>
		<view class="hc-icon-text-text">{{text}}</view>
	</view>
</template>

<script>
	/**
	 * hc-icon-text 图标文字
	 * @description 适用于上部分为图标下部分为文字
	 * @property {String} text 文字
	 * @property {String} icon 图标名
	 * @property {String} iconBgColor 图标背景
	 * @example <hc-icon-text text="标题" :icon="icon-setting"></hc-icon-text>
	 */
	export default {
		name: 'hc-icon-text',
		data() {
			return {

			};
		},
		props: {
			icon: {
				type: String,
				default: ''
			},
			iconBgColor: {
				type: String,
				default: ''
			},
			text: {
				type: String,
				default: ''
			}
		},
		computed: {
			iconStyle() {
				return {
					backgroundColor: this.iconBgColor,
					position: 'relative'
				}
			}
		},
		methods: {
			tapHandle() {
				this.$emit("click");
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-icon-text {
		text-align: center;

		&-icon {
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0 auto;
			width: 91.67rpx;
			height: 91.67rpx;
			border-radius: 12rpx;
			color: #fff;
		}

		&-text {
			font-size: 26rpx;
			font-weight: bold;
			margin-top: 10rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
</style>
