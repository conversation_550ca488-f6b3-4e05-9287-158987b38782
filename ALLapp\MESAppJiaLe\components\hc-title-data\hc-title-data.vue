﻿<template>
	<view class="hc-title-data">
		<view class="hc-title-data-title">
			<view class="hc-title-data-title-text">
				<view class="hc-title-data-title-text-bar" :style="{backgroundColor: $themeCurrent.main}"></view>
				<text>{{ title }}</text>
				<slot name="titleicon"></slot>
			</view>
			<slot name="title"></slot>
		</view>
		<view class="hc-title-data-content">
			<slot name="content"></slot>
		</view>
	</view>
</template>

<script>
	/**
	 * hc-title-data 标题内容
	 * @description 适用于上部分为标题下部分为内容
	 * @property {String} title 标题名称
	 * @example <hc-title-data title="标题"><view>内容</view></hc-title-data>
	 */
	export default {
		name: 'hc-title-data',
		data() {
			return {};
		},
		props: {
			title: String
		}
	};
</script>

<style lang="scss" scoped>
	.hc-title-data {
		padding: 20rpx;
		background-color: #fff;

		&-title {
			display: flex;
			align-items: center;
			font-size: 32rpx;
			font-weight: 700;
			justify-content: space-between;

			&-text {
				display: flex;
				align-items: center;

				&-bar {
					width: 4px;
					height: 34rpx;
					margin-right: 10rpx;
				}
			}
		}

		&-content {
			margin-top: 0rpx;
		}
	}
</style>
