﻿function wranning(str) {
	// 开发环境进行信息输出,主要是一些报错信息
	// 这个环境的来由是在程序编写时候,点击hx编辑器运行调试代码的时候,详见:
	// 	https://uniapp.dcloud.io/frame?id=%e5%bc%80%e5%8f%91%e7%8e%af%e5%a2%83%e5%92%8c%e7%94%9f%e4%ba%a7%e7%8e%af%e5%a2%83
	if (process.env.NODE_ENV === 'development') {
		console.warn(str)
	}
}

// post类型对象参数转为get类型url参数
import queryParams from './function/queryParams.js'
// 路由封装
import route from './function/route.js'
// 时间格式化
import timeFormat from './function/timeFormat.js'
// 时间戳格式化,返回多久之前
import timeFrom from './function/timeFrom.js'
// 颜色渐变相关,colorGradient-颜色渐变,hexToRgb-十六进制颜色转rgb颜色,rgbToHex-rgb转十六进制
import colorGradient from './function/colorGradient.js'
// 生成全局唯一guid字符串
import guid from './function/guid.js'
// 主题相关颜色,info|success|warning|primary|default|error,此颜色已在uview.scss中定义,但是为js中也能使用,故也定义一份
import color from './function/color.js'
// 根据type获取图标名称
import type2icon from './function/type2icon.js'
// 打乱数组的顺序
import randomArray from './function/randomArray.js'
// 对象和数组的深度克隆
import deepClone from './function/deepClone.js'
// 对象深度拷贝
import deepMerge from './function/deepMerge.js'

// 规则检验
import test from './function/test.js'
// 随机数
import random from './function/random.js'
// 去除空格
import trim from './function/trim.js'
// toast提示，对uni.showToast的封装
import toast from './function/toast.js'

// 各个需要fixed的地方的z-index配置文件
import zIndex from './config/zIndex.js'

// 格式化日期字符串带.的问题
import dateStrFormat from './function/dateStrFormat.js'

const collection = {
	queryParams: queryParams,
	route: route,
	timeFormat: timeFormat,
	date: timeFormat, // 另名date
	timeFrom,
	colorGradient: colorGradient.colorGradient,
	guid,
	color,
	type2icon,
	randomArray,
	wranning,
	hexToRgb: colorGradient.hexToRgb,
	rgbToHex: colorGradient.rgbToHex,
	test,
	random,
	deepClone,
	deepMerge,
	trim,
	type: ['primary', 'success', 'error', 'warning', 'info'],
	toast,
	zIndex,
	dateStrFormat
}

export {
	collection
}

export default Vue => {
	// 时间格式化，同时两个名称，date和timeFormat
	Vue.filter('timeFormat', (timestamp, format) => {
		return timeFormat(timestamp, format)
	})
	Vue.filter('date', (timestamp, format) => {
		return timeFormat(timestamp, format)
	})
	Vue.filter('timeFrom', (timestamp, format) => {
		return timeFrom(timestamp, format)
	})
}
