<template>
	<view class="card-out-box" v-if="item" :key="index" @tap="Tap">
		<!-- <hc-read-more :toggle="read" style="width: 100%;text-indent: 0;" show-height="300" > -->
		<view>
			<hc-row>
				<hc-col :span="12">
					<hc-row>
						<hc-col :span="3" class="item-content-title pro-desc">
							<label class="big-title">
								<text>{{model.FSTORE_NAME}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label class="big-title">
								<text>{{item.FSTORE_NAME}}</text>
							</label>
						</hc-col>
						<hc-col :span="3" class="item-content-title pro-desc">
							<label class="big-title">
								<text>{{model.FSTORE_PLACE_NAME}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label class="big-title">
								<text>{{item.FSTORE_PLACE_NAME}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="3" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSTORE_CODE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label>
								<text>{{item.FSTORE_CODE}}</text>
							</label>
						</hc-col>
						<hc-col :span="3" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSTORE_PLACE_CODE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label>
								<text>{{item.FSTORE_PLACE_CODE}}</text>
							</label>
						</hc-col>
						<!-- <hc-col :span="3" class="item-content-title pro-desc">
							<label>
								<text>{{model.FIF_ENABLE_PLACE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label>
								<hc-checkbox v-model="item.FIF_ENABLE_PLACE" readOnly>
								</hc-checkbox>
							</label>
						</hc-col> -->
					</hc-row>
					<hc-row>
						<hc-col :span="3" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSTK_UNIT_QTY}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label>
								<text>{{item.FSTK_UNIT_QTY}}</text>
							</label>
						</hc-col>

						<hc-col :span="3" class="item-content-title pro-desc">
							<label>
								<text>{{model.num}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label>
								<hc-input v-model="item.num" :clearable="false"></hc-input>
							</label>
						</hc-col>
					</hc-row>
					<!-- 		<hc-row>
						<hc-col :span="3" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSTORE_PLACE_NAME}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label>
								<text>{{item.FSTORE_PLACE_NAME}}</text>
							</label>
						</hc-col>
						<hc-col :span="3" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSTORE_PLACE_CODE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="3">
							<label>
								<text>{{item.FSTORE_PLACE_CODE}}</text>
							</label>
						</hc-col>
					</hc-row> -->
					<!-- 	<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FBILL_DATE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FBILL_DATE?item.FBILL_DATE.split(' ')[0]:''}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSEND_DATE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FSEND_DATE?item.FSEND_DATE.split(' ')[0]:''}}</text>
							</label>
						</hc-col>
					</hc-row> -->
				</hc-col>
			</hc-row>
		</view>
		<!-- </hc-read-more> -->
	</view>
</template>

<script>
	export default {
		name: "hc-workPickStore-card",
		props: {
			read: {
				type: Boolean,
				default: true
			},
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				model: {

					FSTORE_NAME: "仓库名称",
					FSTORE_CODE: "仓库编号",
					FIF_ENABLE_PLACE: "启用货位",
					FSTORE_PLACE_NAME: "货位名称",
					FSTORE_PLACE_CODE: '货位编号',

					FSTK_UNIT_QTY: '库存数量', //库存数量
					num: '领料数量', //领料数量

				}
			};
		},
		methods: {
			Tap() {
				this.$emit("click", this.item)
			},
		}
	}
</script>
<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;

		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				text-align: right;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}

		.item-content {

			text-indent: 0;
		}
	}

	.Focus {
		color: red;
		font-weight: 900;
	}

	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.card-out-box {
		margin: 15rpx 20rpx 15rpx 20rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 15rpx 0;
	}

	.big-title {
		// font-size: 30rpx;
		font-weight: bold;
	}
</style>
