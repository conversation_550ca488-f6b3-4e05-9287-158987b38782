<template>
	<view class="item" v-if="item" :key="index">
		<view class="title">
			{{ item.FMATERIAL_NAME||"" }}
		</view>
		<view class="desc">
			<view>规格：{{ item.FSPEC_DESC||"" }}</view>
		</view>
		<view class="desc">
			<view style="width: 50%;"><span class="desc-title">编号：</span>{{ item.FMATERIAL_CODE||"" }}</view>
			<view style="width: 50%;"><span class="desc-title">数量：</span>{{ item.FSALE_UNIT_QTY||"" }}</view>
		</view>
		<view class="desc">
			<view style="width: 50%;"><span class="desc-title">单价：</span>{{ item.FINCLUDE_TAX_PRICE||"" }}</view>
			<view style="width: 50%;"><span class="desc-title">总价：</span>{{ item.FINCLUDE_TAX_AMT||"" }}</view>
		</view>
		<hc-gap height="5"></hc-gap>
	</view>



</template>

<script>
	/*
	 * @example <hc-material-recv-card item="{}" index="0"></hc-material-recv-card>
	 */
	export default {
		name: "hc-materialList-saleOrder-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
			editData: {
				type: Object,
				default () {
					return {
						title: "",
						field: '',
						// fieldQty:'',
					}
				}
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				}
			};
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			},
		}
	}
</script>

<style lang="scss">
	.item {
		// width: 100%;
		// display: flex;
		padding: 6rpx 20rpx;

		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;
	}

	// .outBox {
	// 	padding: 15rpx 12rpx;
	// 	// display: flex;
	// }

	.title {
		font-size: 28rpx;
		line-height: 50rpx;
		font-weight: bold;
		margin: 5rpx 0;
		color: #000000 !important;
	}

	.desc {
		display: flex;
		// color: $u-tips-color;
		color: #66686c;
		margin: 10rpx 0;
	}

	.desc view {
		// width: 36%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.desc-title {}
</style>
