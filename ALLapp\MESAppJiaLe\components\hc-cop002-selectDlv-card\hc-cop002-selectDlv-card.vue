<template>
	<view class="order-box">
		<view v-if="item" :key="index" @tap="clickOrder(item)">
			<view class="item">
				<view style="flex: 1;" class="item-content">
					<hc-row>
						<hc-col :span="12">
							<view class="item-content">
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										出货单编号:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;">
										{{ item.FSALE_DLV_NO||"" }}
									</view>
								</view>
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										客户名称:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;">
										{{ item.FCUST_NAME||"" }}
									</view>
								</view>
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										项目名称:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;">
										{{ item.FPROJECT_NAME||"" }}
									</view>
								</view>
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										销货日期:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;">
										{{ this.$hc.Command.FormatDate(item.FBILL_DATE)||"" }}
									</view>
								</view>
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										未出库产品:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;" :style="customStyle">
										{{ item.NotPostNum||0 }}
									</view>
								</view>
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										未出库行数:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;" :style="customStyle">
										{{ item.NotPostRowNum||0 }}
									</view>
								</view>
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										本部门未出库产品:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;" :style="customStyle">
										{{ item.NotDepPostNum||0 }}
									</view>
								</view>
								<view class="item-content-type pro-left">
									<view class="" style="display: inline-block;">
										本部门未出库行数:
									</view>
									<view class="" style="float: right;padding-right: 20rpx;" :style="customStyle">
										{{ item.NotDepPostRowNum||0 }}
									</view>
								</view>
							</view>
						</hc-col>
					</hc-row>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"hc-cop002-selectDlv-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
				},
			};
		},
		computed: {
		
		},
		methods: {
			clickOrder(item) {
				this.$emit("clickOrder", item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-box {
		position: relative;
		background-color: #eef1f5;
		padding: 20rpx
	}

	.item {
		width: 100%;
		display: flex;

		border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 20rpx;
			width: 180rpx;
			height: 180rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 20rpx 0rpx 0rpx 0;
				font-size: 30rpx;
				border-bottom: 1rpx solid #e8eaf0;
				padding: 10rpx 10rpx 30rpx 20rpx;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-left {}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
