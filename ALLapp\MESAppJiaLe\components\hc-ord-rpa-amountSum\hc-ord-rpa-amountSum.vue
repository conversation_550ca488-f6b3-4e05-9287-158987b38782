﻿<template>
	<view class="item" v-if="$haveAuth">
		<view v-for="(item, index) in Mst" :key="index">
			<hc-row>
				<hc-col :span="12">
					<text style="white-space: nowrap;">{{item.ShowText}}(万)</text>
				</hc-col>
			</hc-row>
			<hc-gap bg-color="#FFFFFF" :height="10"></hc-gap>
			<hc-row>
				<hc-col :span="12">
					<text class="item-amount">{{item.Amount}}</text>
					<hc-icon :color="item.Up?'#F54053':'#2CC180'" :name="item.Up ?'arrow-upward':'arrow-downward'"></hc-icon>
				</hc-col>
			</hc-row>
			<hc-gap bg-color="#FFFFFF" :height="30" v-if="index<Mst.length-1"></hc-gap>
		</view>
	</view>
</template>

<script>
	/**
	 * hc-ord-rpa-amountSum RPA节点金额统计
	 * @example <hc-ord-rpa-amountSum rpaNodeCode="jiedan" Type="day"></hc-ord-rpa-amountSum>
	 */
	export default {
		name: 'hc-ord-rpa-amountSum',
		data() {
			return {
				api: {
					StatisticsRpaNodeAmount: "/api/COP002RpaQuery/StatisticsRpaNodeAmountAsync",
				},
				Mst: [],
			};
		},
		props: {
			RpaNodeCode: {
				type: [Array, String],
				default: []
			},
			//day month year
			Type: {
				type: String,
				default: ''
			}
		},
		created() {
			this.LoadData();
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			},
			LoadData() {
				var _this = this;
				if (_this.rpaNodeCode == "") {
					return;
				}
				var option = {
					url: _this.api.StatisticsRpaNodeAmount,
					data: {
						model: {
							IsAll: false,
							Type: _this.Type,
							NodeCodes: _this.RpaNodeCode,
						}
					},
				};
				this.$hc.request(option).then(res => {
					if (res.StatusCode != 200) {
						_this.$hc.Command.ShowToast({
							title: res.Message,
						});
					} else {
						_this.Mst=[];
						res.Entity.forEach(function(item) {
							if (_this.Type == "day") {
								_this.Mst.push({
									Amount: _this.$hc.Command.MathFixedFun(parseFloat(item.DayAmount) / 10000),
									Up: item.DayUp,
									ShowText: "本日" + item.RpaNodeName + "额"
								});
							} else if (_this.Type == "month") {
								_this.Mst.push({
									Amount: _this.$hc.Command.MathFixedFun(parseFloat(item.MonthAmount) /10000),
									Up: item.MonthUp,
									ShowText: "本月" + item.RpaNodeName + "额"
								});
							} else {
								_this.Mst.push({
									Amount: _this.$hc.Command.MathFixedFun(parseFloat(item.YearAmount) /10000),
									Up: item.YearUp,
									ShowText: "本年" + item.RpaNodeName + "额"
								});
							}
						});
					}
				});
			}
		},
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		padding: 15rpx 15rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;

		&-amount {
			width: 100rpx;
			font-size: 32rpx;
			font-weight: 700;
		}
	}

	.amountText {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>
