<template>
	<hc-input v-model="innerValue" :type='type' :clearable="clearable" :inputAlign="inputAlign"
		:placeholder='i18nPlaceholder' :disabled="disabled" :maxlength="maxlength" :placeholderStyle="placeholderStyle"
		:confirmType="confirmType" :customStyle="customStyle" :focus="focus" :autoHeight="autoHeight"
		:borderColor="borderColor" :dataColor="dataColor" @input='handleInput' @blur='handleBlur' @focus="focus"
		:border="border" @clear="onClear">
		<hc-icon :customStyle="{padding:'0px 10rpx'}" @click="openPage" slot="right" size="40" name="search"
			:style="{color:tcolor}">
		</hc-icon>
	</hc-input>
</template>

<script>
	export default {
		name: 'hc-open-search',
		data() {
			return {
				innerValue: this.value,
				emitCode: null
			}
		},
		props: {
			value: [String, Number],
			// 输入框的类型，textarea，text，number
			type: {
				type: String,
				default: 'text'
			},
			clearable: {
				type: Boolean,
				default: true
			},
			border: {
				type: Boolean,
				default: true
			},
			inputAlign: {
				type: String,
				default: 'left'
			},
			placeholder: {
				type: String,
				default: 'PLEASE_ENTER'
			},
			disabled: {
				type: Boolean,
				default: false
			},
			maxlength: {
				type: [Number, String],
				default: 140
			},
			placeholderStyle: {
				type: String,
				default: 'color: #c0c4cc; white-space: normal;'
			},
			confirmType: {
				type: String,
				default: 'done'
			},
			customStyle: {
				type: Object,
				default () {
					return {};
				}
			},
			// 是否自动获得焦点
			focus: {
				type: Boolean,
				default: false
			},
			// 输入框的边框颜色
			borderColor: {
				type: String,
				default: '#dcdfe6'
			},
			autoHeight: {
				type: Boolean,
				default: true
			},
			// type=select时，旋转右侧的图标，标识当前处于打开还是关闭select的状态
			// open-打开，close-关闭
			selectOpen: {
				type: Boolean,
				default: false
			},
			// 高度，单位rpx
			height: {
				type: [Number, String],
				default: ''
			},
			dataColor: {
				type: String,
				default: ''
			},
			dataType: String,
			headerName: String,
			befOpenFunName: Function,
			beforeOpenSearch: Function,
			onlyCode: String,
			isOpenPage: {
				type: Boolean,
				default: true
			}
		},
		watch: {
			value(newValue) {
				if (newValue !== this.innerValue) {
					this.innerValue = newValue;
				}
			},
		},
		methods: {
			handleInput(val) {
				this.$emit('input', val);
			},
			handleBlur(val) {
				this.$emit('blur', val);
			},
			onFocus(event) {
				this.$emit('focus');
			},
			onConfirm(val) {
				this.$emit('confirm', val);
			},
			onClear(event) {
				this.$emit('input', '');
				this.$emit('clear');
			},
			openPage() {
				let val = this.innerValue || ""
				const _this = this;
				const hc = this.$hc;
				const dataType = this.dataType;
				const obj = {
					val,
					dataType,
				}
				if (this.headerName) obj.headerName = this.headerName;
				//不为空的时候才监听
				if (!hc.Command.IsEmpty(this.onlyCode)) {
					let emitCode = `${this.onlyCode}_OpenSearch`;
					obj.emitCode = emitCode;
					this.emitCode = emitCode
					//监听
					uni.$on(emitCode, (arg, option) => {
						if (hc.Command.IsFunction(this.beforeOpenSearch)) {
							this.beforeOpenSearch(arg, option)
						}
					})
				}
				if (this.isOpenPage) {
					uni.$on('afterSelectData', res => _this.afterSelectData(res));
					hc.Command.NavigateToByUrl("/pages/Common/OpenSearchPage", obj);
				}

			},
			afterSelectData(arg) {
				const hc = this.$hc;
				uni.$off('afterSelectData')
				if (!hc.Command.IsEmpty(this.emitCode)) {
					uni.$off(this.emitCode)
				}
				uni.$off('afterSelectData')
				this.$emit('afterSelectData', arg)
			},
		},
		computed: {
			i18nPlaceholder() {
				return this.$hc.t(this.placeholder)
			},
			tcolor() {
				const {
					main
				} = this.$themeCurrent
				return main
			},
		},

	}
</script>

<style>
</style>