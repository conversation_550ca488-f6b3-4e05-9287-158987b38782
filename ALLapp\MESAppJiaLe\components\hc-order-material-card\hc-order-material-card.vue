<template>
	<hc-layout>
		<hc-navbar :title="editData.toptitle">
		</hc-navbar>
		<view>
			<hc-form :model="model" ref="form1" :errorType="errorType">
				<view @touchmove.stop='' style="height: 100%;">
					<hc-form :model="model" ref="form2" :errorType="errorType">
						<hc-form-item :label="`${editData.searchtitle}:`" label-width="auto" prop="storeQrcode">
							<hc-input type="text" v-model="storeQrcode" input-align="right" placeholder="请扫码或输入编号"
								maxlength="50" class="input-prompt" :focus="storeQrcodeFocus"
								@confirm="storeQrcodeChange">
							</hc-input>
							<hc-icon icon="icon-scan" @click="storeQrcodeHandle" size="50"></hc-icon>
						</hc-form-item>
						<hc-form-item label="物料:" label-width="auto" prop="materialQrcode">
							<hc-input type="text" v-model="materialQrcode" input-align="right" placeholder="请扫描物料/成品编号"
								maxlength="50" class="input-prompt" :focus="materialQrcodeFocus"
								@blur="materialQrcodeChange" @confirm="materialQrcodeChange">
							</hc-input>
							<hc-icon icon="icon-scan" @click="materialQrCodeHandle" size="50"></hc-icon>
						</hc-form-item>
					</hc-form>
				</view>
				<hc-button-group :full="true" style="padding: 7rpx 10rpx ;">
					<hc-button type="primary" class="hc-button-full" isGroup isGroupFirst @tap="stocking">
						出&nbsp&nbsp库
					</hc-button>
					<hc-button type="primary" class="hc-button-full" isGroup isGroupLast @tap="empty">
						清&nbsp&nbsp空
					</hc-button>
				</hc-button-group>
			</hc-form>
		</view>
		<hc-gap :bg-color="borderColor" height="5"></hc-gap>

		<view class="hc-layout-full">
			<view>
				<hc-tabs-swiper ref="uTabs" :list="tabList" :current="tabcurrent" @change="tabsChange"
					:active-color="$hc.requestConfig.dataColor.warn" :bar-height="8" :bar-width="barWidth"
					:is-scroll="false" swiperWidth="750"></hc-tabs-swiper>
			</view>
			<swiper :current="tabcurrent" @transition="transition" @animationfinish="animationfinish"
				style="height: 100%;">
				<swiper-item style="height: 100%;">
					<hc-scroll ref="hcScroll" @refresh="onRefresh" @loadmore="reachBottom">
						<hc-uni-swipe-action v-if="orderList!=null&&orderList.length>0">
							<hc-uni-swipe-action-item :extra="item" :index="index" v-for="(item, index) in orderList"
								:key="index" :right-options="swipe.options">
								<view class="item" @click="ItemClick(item,index)">
									<view style="flex: 1;" class="item-content">
										<hc-row>
											<hc-col :span="12">
												<view class="item-content">
													<hc-row>
														<hc-col :span="12">
															<view class="item-content-type pro-left">
																名称：{{ item.FMATERIAL_NAME||"" }}</view>
														</hc-col>
													</hc-row>
													<hc-row>
														<hc-col :span="6">
															<view class="item-content-type pro-left">
																编号：{{ item.FMATERIAL_CODE||"" }}</view>
														</hc-col>
														<hc-col :span="6">
															<!-- 库存单位 -->
															<view class="item-content-type pro-left">
																单位：{{ item.FSTK_UNIT_NAME||"" }}</view>
														</hc-col>
													</hc-row>
													<hc-row>
														<!-- 	<hc-col :span="6">
															<view class="item-content-type pro-left">仓库：<span
																	@click="stkclick(item,index)"
																	:style="{'color':$hc.requestConfig.dataColor.warn,'font-weight':'bold'}">{{ !$hc.Command.IsEmpty(item.FSTORE_NAME)? (!$hc.Command.IsEmpty(item.FSTORE_PLACE_NAME)?item.FSTORE_NAME+"/"+item.FSTORE_PLACE_NAME:item.FSTORE_NAME):"" }}</span>
															</view>
														</hc-col> -->
														<hc-col :span="6">
															<view class="item-content-type pro-left">
																已{{editData.title}}：：{{ item.FSCAN_NUM||0}}
															</view>
														</hc-col>
														<hc-col :span="6">
															<!-- 数量 -->
															<view class="item-content-type pro-left">
																{{editData.title}}：{{ item.FNUM||0}}
															</view>
														</hc-col>
													
													</hc-row>
												<!-- 	<hc-row>
														<hc-col :span="6">
															<view class="item-content-type pro-left">
																标签数：<hc-input type="number"
																		v-model="item[editData.field]"
																		:customStyle="customStyle"></hc-input></view>
														</hc-col>
													</hc-row> -->
												</view>
											</hc-col>
										</hc-row>
									</view>
								</view>
							</hc-uni-swipe-action-item>

						</hc-uni-swipe-action>
					</hc-scroll>
				</swiper-item>
				//扫描记录
				<swiper-item style="height: 100%;">
				</swiper-item>

			</swiper>

		</view>
		<view>
			<hc-select v-model="Selshow" mode="mutil-column-auto" :list="Stklist" @confirm="Selconfirm"> </hc-select>
			<!-- <hc-modal v-model="Moshow" :title-style="{color: 'red'}">
				<view class="slot-content">
					<view class="item-content-type pro-left">仓库：<span @click="stkclick(item,ItemIndex)"
							:style="{'color':$hc.requestConfig.dataColor.warn,'font-weight':'bold'}">{{ !$hc.Command.IsEmpty(item.FSTORE_NAME)? (!$hc.Command.IsEmpty(item.FSTORE_PLACE_NAME)?item.FSTORE_NAME+"/"+item.FSTORE_PLACE_NAME:item.FSTORE_NAME):"" }}</span>
					</view>
				</view>
			</hc-modal> -->
		</view>
	</hc-layout>
</template>

<script>
	export default {
		name: "hc-material-other-card",
		props: {

			index: {
				type: Number,
				default: 0
			},
			editData: {
				type: Object,
				default () {
					return {
						title: "出库数",
						field: "FNUM",
						toptitle: "委外出库",
						billtype: "OutOrderOutScan",
						searchtitle: "委外单号"
					}


				}
			}
		},
		data() {
			return {

				selectWorkStationId: [0],
				finishstr: "",
				qrcodecopy: "",
				weightdisable: false,
				tabList: [{
					name: "物料清单"
				}, {
					name: "操作记录"
				}],
				tabcurrent: 0,
				barWidth: 250,
				api: {
					apiGetItemList: "/api/MES017Tag/GeneratePredictBomCodeList", //获取出入库清单
					apiGetStore: "/api/STK001Store/GeStoreAndPlaceAsync", //获取仓库和对应的货位
					apiStocking: "/api/STK003OtherIOBill/StockingAsync", //入库api

				},
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				},
				show: false,
				Stklist: [],
				Moshow: false,
				ItemIndex: 0,
				Selshow: false,
				stations: [],
				borderColor: this.$hc.color.borderColor,
				errorType: ['message'],


				modelCopy: "", //初始化时备份一份初始的model数据，清空时还原

				statusColor: {
					color: "#ffffff",
					"background-color": "#ffffff"
				},

				inputUserStyle: {},


				swipe: {
					options: [{
						text: '删除',
						style: {
							backgroundColor: '#007aff',
							margin: '10rpx 0rpx 10rpx -8rpx',
							borderRadius: '40rpx',
						},
						"app-plus": {
							"bounce": "none"
						}
					}]
				}, //滑动选项

				models: [], //数据列表


				//判断是否启用货位管理变量
				havePlace: true,

				storeQrcode: '', //货位编号二维码
				storeQrcodeFocus: true, //货位二维码获焦
				materialQrcode: "", //物料扫码编号
				materialQrcodeFocus: false, //物料扫码框聚焦属性
				orderList: [],
				originModel: "", //父页转递来的数据
				currentItemIndex: null,
				model: { //表格数据
					// FPROC_ORDER_ID: "", //采购订单id
					// FPROC_ORDER_NO: "", //订单编号
					// FPROVIDER_NAME: '', //供应商编号
					// FCONFIRM_DATE: "", //订单交期
					// FPROC_EMP_NAME: "", //采购员
					// MaterialCount: '', //订单产品种类

					FSTORE_ID_NAME: '', //仓库名称
					FSTORE_ID: "",
					FSTORE_PLACE_NAME: '', //货位名称
					FSTORE_PLACE_CODE: "",
					FSTORE_PLACE_ID: "",
				}
			}
		},

		mounted(option) {

			this.modelCopy = JSON.parse(JSON.stringify(this.model)) //初始化时备份一份初始的model数据，清空时还原

			this.swipe.options[0].style.backgroundColor = this.$themeCurrent.main

			const tabCount = this.tabList.length;
			this.barWidth = 750 / tabCount; // 简单等分逻辑（单位：rpx）
			this.setWarnColor()
			this.getStoreData()

		},
		onReady() {

		},
		methods: {
			//条目点击
			ItemClick(item, index) {
				this.Moshow = true;
				this.ItemIndex = index;
			},
			// swiper-item左右移动，通知tabs的滑块跟随移动
			transition(e) {
				let dx = e.detail.dx;
				this.$refs.uTabs.setDx(dx);
			},
			// 由于swiper的内部机制问题，快速切换swiper不会触发dx的连续变化，需要在结束时重置状态
			// swiper滑动结束，分别设置tabs和swiper的状态
			animationfinish(e) {
				let current = e.detail.current;
				this.$refs.uTabs.setFinishCurrent(current);
				this.swiperCurrent = current;
				this.current = current;
			},
			stkclick(item, index) {
				this.currentItemIndex = index
				this.Selshow = true
			},
			tabsChange(val) {
				console.log(val)
				this.tabcurrent = val
			},
			Selconfirm(val) {
				console.log(val)
				if (!val || val.length === 0) return;

				const storeId = val[0]?.value || '';
				const placeId = val[1]?.value || '';
				const storeName = val[0]?.label || '';
				const placeName = val[1]?.label || '';

				const item = this.orderList[this.currentItemIndex];
				if (item) {
					item.FSTORE_ID = storeId;
					item.FSTORE_NAME = storeName;
					item.FSTORE_PLACE_ID = placeId;
					item.FSTORE_PLACE_NAME = placeName;
				}
			},
			//根据单号获取数据
			getOrderList() {

				let _this = this;
				if (_this.$hc.Command.IsEmpty(_this.storeQrcode)) {
					return
				}
				let OrderInf = {
					"FBILL_SOURCE_NO": _this.storeQrcode,
					"FBILL_TYPE": _this.editData.billtype
				};
				_this.$hc.Command.LoadData(_this.api.apiGetItemList, {
					model: OrderInf
				}, (res) => {
					if (res.StatusCode == 200) {
						_this.orderList = res.Entity.TagBillList
					}
				})

			},
			//根据货位号获取货位信息方法
			getStoreData() {
				let _this = this;
				console.log('22222')
				_this.$hc.Command.LoadData(_this.api.apiGetStore, {}, (res) => {
					if (res.StatusCode === 200) {
						if (res.Entity.length > 0) {
							const cascaderOptions = res.Entity.map(store => {
								const node = {
									value: store.FSTORE_ID,
									label: store.FSTORE_NAME

								};

								if (store.FIF_ENABLE_PLACE && store.PLACE_LIST.length > 0) {
									node.children = store.PLACE_LIST.map(place => ({
										value: place.FSTORE_PLACE_ID,
										label: place.FSTORE_PLACE_NAME
									}));
								}

								return node;
							});
							cascaderOptions.sort((a, b) => {
								const aHasChildren = a.children && a.children.length > 0;
								const bHasChildren = b.children && b.children.length > 0;
								return aHasChildren === bHasChildren ? 0 : aHasChildren ? -1 : 1;
							});
							console.log(cascaderOptions);
							_this.Stklist = cascaderOptions
						}

					} else {
						_this.storeQrcode = ''
						_this.$hc.Command.ShowResMessage(res);
					}
				})

			},
			//失焦，输入时方法 change
			storeQrcodeChange() {
				this.getOrderList()
			},
			//搜索图标方法
			storeQrcodeHandle() {
				let _this = this;
				// 扫码图标点击
				uni.scanCode({
					success(res) {
						_this.storeQrcode = res.result;
						_this.getStoreData();
					},
					fail(res) {},
					complete(res) {},
				});
			},


			//扫描后根据单据编号、物料编码，搜索方法
			getMaterialData() {
				let _this = this;

				if (!_this.materialQrcode) {
					return
				}

				let model = {
					"PageSize": 30,
					"PageIndex": 1,
					"WhereGroup": {
						"Groups": [{
							"Items": [{
								"FieldName": "a.FMATERIAL_CODE",
								"FieldDataType": "",
								"OperatorType": "Equal",
								"Value": _this.materialQrcode
							}, ],
							"GroupType": "OR"
						}],
						"Items": [],
						"GroupType": "AND"
					}
				}
				_this.$hc.Command.LoadData(_this.api.apiGetOrderMaterial, {
					model
				}, (res) => {
					if (res.StatusCode == 200) {
						if (res.Entity.length > 0) { //每次添加不只有一个物料
							_this.formatModels(res.Entity) //格式化models方法
						} else {
							return _this.$hc.Command.NotifyDialog({
								content: "未找到对应物料",
								confirm() {}
							});
						}
					} else {
						_this.$hc.Command.ShowResMessage(res);
					}
				})
			},
			//扫描图标方法
			materialQrCodeHandle: function() {
				let _this = this;
				// 扫码图标点击
				uni.scanCode({
					success(res) {
						_this.materialQrcode = res.result;
						_this.getMaterialData();
					},
					fail(res) {

					},
					complete(res) {

					},
				});
			},
			//物料二维码 change
			materialQrcodeChange() {
				var _this=this
				if (_this.$hc.Command.IsEmpty(_this.storeQrcode)) {
					return _this.$hc.Command.NotifyDialog({
						content: "对应单号不能为空！",
						confirm() {
							_this.materialQrcode=''
						}
					});
					return
				}
				// this.getMaterialData()
			},

			//格式化Models方法
			formatModels(data) {
				let models = this.models.slice()
				for (let i of data) {
					let ifget = false;
					for (let j of models) {
						if (i.FMATERIAL_ID == j.FMATERIAL_ID) {
							ifget = true;
							continue;
						}
					}
					if (!ifget) {
						models.unshift(i) //在数组添加第一位数组
						i.FSTK_UNIT_QTY = '' //将入库数默认设置为0
					}
				}
				this.models = models

			},
			//入库方法
			stocking() {
				let _this = this
				let Materials = this.models

				if (!this.model.FSTORE_PLACE_NAME) { //如果没有货位号
					if (!this.model.FSTORE_ID_NAME) { //如果没有仓库号
						return this.$hc.Command.NotifyDialog({
							content: "请先输入有效的仓库/货位编号",
							confirm() {}
						});
					} else {
						if (this.havePlace) { //如果已启用货位管理
							return this.$hc.Command.NotifyDialog({
								content: "该仓库已启用货位管理,请输入有效的货位编号",
								confirm() {}
							});
						}
					}
				}

				if (Materials.length == 0) {
					return this.$hc.Command.NotifyDialog({
						content: `请录入物料数据`,
						confirm() {}
					});
				}

				for (let i of Materials) {
					if (i.FSTK_UNIT_QTY == 0 || i.FSTK_UNIT_QTY < 0) {
						return this.$hc.Command.NotifyDialog({
							content: `${i.FMATERIAL_NAME}的入库数输入错误`,
							confirm() {}
						});
					}
				}

				let model = {
					FSTORE_ID: this.model.FSTORE_ID,
					FSTORE_PLACE_ID: this.model.FSTORE_PLACE_ID,
					Materials
				}

				_this.$hc.Command.LoadData(_this.api.apiStocking, {
					model
				}, (res) => {
					if (res.StatusCode == 200) {
						_this.$hc.Command.ToSaveSuccessPage({
							buttons: [{
								pageUrl: "OtherStockingApp",
								text: "继续入库"
							}, ]
						})
						// _this.$hc.Command.NotifyDialog({
						// 	content: "入库完成",
						// 	confirm() {
						// 		_this.emptyMaterials() //完成之后清空物料信息
						// 		// uni.navigateBack();
						// 	}
						// });
					} else {
						_this.$hc.Command.ShowResMessage(res);

						// _this.$hc.Command.NotifyDialog({
						// 	content: "未找到对应物料",
						// 	confirm() {}
						// });
					}
				}, {
					disableLoading: false
				})
			},
			onRefresh() {
				// this.$refs.hcScroll.mescroll.endBySize(rowData.length, res.Pager.TotalRecords);

				this.$refs.hcScroll.mescroll.endBySize(10, this.models.length);
			},
			reachBottom() {
				this.$refs.hcScroll.mescroll.endBySize(10, this.models.length);
			},
			//写入警告色
			setWarnColor() {
				let color = `${this.$hc.requestConfig.dataColor.warn}`
				this.inputUserStyle["color"] = color
			},
			//设定员工信息
			setEmpInfo: function() {
				let userInfo = this.$hc.Command.GetUserInfo();
				if (userInfo) {
					this.model.FEMP_NAME = userInfo.UserPsnName;
					this.model.FEMP_ID = userInfo.UserPsnId;
				}
			},
			toNavBack() {
				this.$hc.Command.SwitchTab("IndexPage");
			},
			//清空方法
			empty() {
				this.emptyMaterials()
				this.emptyStore()
			},
			//清空物料方法
			emptyMaterials() {
				this.models = []
				this.materialQrcode = ''
			},
			//清空仓库信息方法
			emptyStore() {
				this.model = this.modelCopy
				this.storeQrcode = ''
				this.havePlace = true
			},
			//左滑删除按钮方法
			onClick(index) {
				let models = this.models
				models.splice(index, 1)
			},
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			}


		},

		computed: {


		}
	}
</script>

<style lang="scss" scoped>
	.pic-Image {
		flex: 0 0 420rpx;
		height: 420rpx;
		display: flex;
		background-color: #FFFFFF;
	}

	.hc-popup-top {
		text-align: center;
		line-height: 70rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.unStart {
		background-color: #FFFFFF;
		color: #ffffff;
	}

	.cancel {
		background-color: #FFFFFF;
		color: #ffffff;
	}

	.working {
		background-color: #00aa00;
		color: #ffffff;
	}

	.partfinished {
		background-color: #00aa00;
		color: #ffffff;
	}

	.paused {
		background-color: #ffff7f;
		color: #ffffff;
	}

	.input-prompt {
		font-weight: 900;
	}

	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-bottom: #d1d1d1 solid 2rpx !important;
		;
		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 20rpx;
			width: 160rpx;
			height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-left {
		// width: 500rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>

<!-- 
<script>
	/*
	 * @example <hc-material-recv-card item="{}" index="0"></hc-material-recv-card>
	 */
	export default {
		name: "hc-material-other-card",
		props: {

			index: {
				type: Number,
				default: 0
			},
			editData: {
				type: Object,
				default () {
					return {
						title: "",
						field: '',
						// fieldQty:'',
					}
				}
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				},
			};
		},
		methods: {

		}
	}
</script> -->