﻿<template>
	<view class="hc-button-group" :class="[
		full ? 'hc-button-group-full':'',
		]">
		<slot></slot>
	</view>
</template>
<script>
	/**
	 * hc-button-group 按钮组
	 * @description Button 按钮组
	 * @property {Boolean} full 按钮等比是否填充满
	 * @example <hc-button-group>
					<hc-button size="mini" isGroup isGroupFirst shape="square">
						年
					</hc-button>
					<hc-button size="mini" isGroup  >
						月
					</hc-button>
					<hc-button size="mini" isGroup    >
						周
					</hc-button>
					<hc-button size="mini" isGroup  isGroupLast  shape="square">
						日
					</hc-button>
				</hc-button-group>
				
				<hc-button-group :full="true">
					<hc-button class="hc-button-full" isGroup isGroupFirst @click="resetFilter">重置</hc-button>
					<hc-button class="hc-button-full" isGroup isGroupLast type="primary" @click="confirmFilter">确认</hc-button>
				</hc-button-group>
	 */
	export default {
		name: 'hc-button-group',
		props: {
			full: {
				type: Boolean,
				default: false
			}
		}
	};
</script>

<style scoped lang="scss">
	.hc-button-group {
		&-full {
			display: flex;
			flex-direction: row;
		}

	}
	
</style>
