﻿<template>
	<view class="item" v-if="item" :key="index">
		<view class="item-left">
			<hc-image :src="$hc.Command.GetAttachmentURL(item.FATTACHMENT_ID)"></hc-image>
		</view>
		<view style="flex: 1;" class="item-content">
			<hc-row>
				<hc-col :span="6">
					<view class="item-content">
						<view class="item-content-title pro-left">
							{{ item.FEMP_NAME||"" }}
							<hc-icon :icon="item.FSEX==1?'icon-man':'icon-woman'" :size="30" :color="item.FSEX==1?'rgb(64, 158, 255)':'rgb(255, 73, 204)'"></hc-icon>
						</view>
						<view class="item-content-type pro-left">工号:{{ item.FEMP_CODE||"" }}</view>
						<view class="item-content-type pro-left">组织:{{ item.FMAKER_NAME||"" }}</view>
					</view>
				</hc-col>
				<hc-col :span="6">
					<view class="item-right">
						<view class="item-content-title pro-right">手机:{{ item.FMOBILE||"" }}</view>
						<view class="item-content-type pro-right">邮箱:{{ item.FMAIL_BOX||"" }}</view>
						<view class="item-content-type pro-right">岗位:{{ item.FSTATION||"" }}</view>
					</view>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	/*
	 * @example <hc-employee-card item="{}" index="0"></hc-employee-card>
	 */
	export default {
		name: "hc-employee-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				
			};
        },
        methods: {
            MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
            }
        }
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		
		//background-color: red;
		//border-radius: 16rpx;
		background-color: #ffffff;
		
		&-left {
			margin-right: 20rpx;
			width: 160rpx;
			height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				//color: $u-tips-color;
			}

			&-delivery-time {
				//color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-left {
		width: 240rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
