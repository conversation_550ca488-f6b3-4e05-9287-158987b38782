﻿<template>
	<view class="item" v-if="item" :key="index">
		<view class="item-left">
			<hc-image :src="$hc.Command.GetAttachmentURL(item.FATTACHMENT_ID)"></hc-image>
		</view>
		<view style="flex: 1;" class="item-content">
			<hc-row>
				<hc-col :span="12">
					<view class="item-content">
						<view class="item-content-type pro-left">材质名称:{{ item.FMETIAL_NAME||"" }}</view>
						<view class="item-content-type pro-left">材质代号:{{ item.FMETIAL_CODE||"" }}</view>
						<view class="item-content-type pro-left">材质分类:{{ item.FMETIAL_SORT_NAME||"" }}</view>
					</view>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	/*
	 * @example <hc-material-card item="{}" index="0"></hc-material-card>
	 */
	export default {
		name: "hc-material-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				
			};
        },
        methods: {
            MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
            }
        }
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		
		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;
		
		&-left {
			margin-right: 20rpx;
			width: 160rpx;
			height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-left {
		width: 500rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
