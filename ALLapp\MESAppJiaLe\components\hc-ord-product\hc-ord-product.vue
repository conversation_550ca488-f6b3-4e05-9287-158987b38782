﻿<template>
	<view class="item" v-if="item" :key="index">
		<view class="item-left">
			<hc-image :src="$hc.Command.GetAttachmentURL(item.FATTACHMENT_ID)"></hc-image>
		</view>
		<view style="flex: 1;">
			<hc-row>
				<hc-col :span="7">
					<view class="item-content">
						<view class="item-content-title pro-desc">{{ item.FPRODUCT_NAME||"" }}</view>
					</view>
				</hc-col>
				<hc-col :span="5">
					<view class="item-right">
						<view class="price pro-num">
							￥{{ item.FPRICE||"0" }}
						</view>
					</view>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="7">
					<view class="item-content">
						<view class="item-content-type pro-desc">品号:{{ item.FPRODUCT_CODE||"" }}</view>
					</view>
				</hc-col>
				<hc-col :span="5">
					<view class="item-right">
						<view class="item-content-type pro-num">数量:{{ item.FQTY||"0" }}</view>
					</view>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="7">
					<view class="item-content">
						<view class="item-content-type pro-desc">规格:{{ item.FPRODUCT_DESC||"" }}</view>
					</view>
				</hc-col>
				<hc-col :span="5">
					<view class="item-right">
						<view class="item-content-type" v-if="item.FRATE<10">{{ MathFixedFun(item.FRATE) }} 折</view>
					</view>
				</hc-col>
			</hc-row>
			<hc-row v-if="item.FCUST_PROPERTY_DESC!=''&&item.FCUST_PROPERTY_DESC!=null">
				<hc-col :span="12">
					<view class="item-content">
						<view class="item-content-type row-pro-desc">定制:{{ item.FCUST_PROPERTY_DESC||"" }}</view>
					</view>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	/*
	 * @example <hc-ord-product item="{}" index="0"></hc-ord-product>
	 */
	export default {
		name: "hc-ord-product",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {

			};
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			}
		}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 160rpx;
			height: 185rpx;
			padding-top: 25rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-desc {
		width: 260rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.row-pro-desc {
		width: 460rpx;
		word-wrap: break-word;
		word-break: break-all;
		overflow: hidden;
	}

	.pro-num {
		//width: 170rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
