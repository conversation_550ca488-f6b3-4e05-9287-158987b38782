﻿<template>
	<view class="item" v-if="item" :key="index">
		<!-- <view class="item-left">
			<hc-icon :icon="item.FOPP_STATUS_ICON" :color="item.FOPP_STATUS_COLOR"></hc-icon>
		</view>
		<view style="flex: 1;"> -->
			<hc-row>
				<hc-col :span="6">
					<view class="item-content" style="display: flex;align-items: center;">
						<hc-icon :icon="item.FOPP_STATUS_ICON" :color="item.FOPP_STATUS_COLOR" style="padding-right: 10rpx;"></hc-icon>
						<view class="item-content-title pro-desc" >{{ item.FOPP_NAME||"" }}</view>
					</view>
				</hc-col>
				<hc-col :span="6">
					<view class="item-right">
						<view class="item-content-type pro-desc">
							{{ item.FOPP_STEP_NAME||"" }}
						</view>
					</view>
				</hc-col>
			</hc-row> 
			<hc-row>
				<hc-col :span="6">
					<view class="item-content">
						<view class="item-content-type pro-desc">姓名：{{ item.FTER_CUST_NAME||"" }}</view>
					</view>
				</hc-col>
				<hc-col :span="6">
					<view class="item-right">
						<view class="item-content-type pro-desc">
							<hc-icon name="phone-fill" :size="30" :color="$themeCurrent.main"></hc-icon>
							{{ item.FTER_CUST_MOBILE||"" }}
						</view>
					</view>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="6">
					<view class="item-content">
						<view class="item-content-type" :style="'color:'+$themeCurrent.main">￥{{ item.FOPP_AMOUNT||"0" }}</view>
					</view>
				</hc-col>
				<hc-col :span="6">
					<view class="item-right">
						<view class="item-content-type pro-desc">负责人：{{ item.FLEADER||"" }} </view>
					</view>
				</hc-col>
			</hc-row>
		<!-- </view> -->
	</view>
</template>

<script>
	/*
	 * @example <hc-opp-item item="{}" index="0"></hc-opp-item>
	 */
	export default {
		name: "hc-opp-item",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {

			};
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			}
		}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		// display: flex;
		padding: 15rpx 12rpx;

		&-left {
			margin-right: 20rpx;
			width: 120rpx;
			height: 160rpx;
			text-align: center;
			line-height: 160rpx;
			font-size: 90rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				font-weight: bold;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
				width:100%;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-desc {
		width: 260rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.pro-num {
		//width: 170rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
