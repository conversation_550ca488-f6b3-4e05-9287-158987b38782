<template>
	<view class="card-out-box item" v-if="item" :key="index" @tap="Tap" >
		<view>
			<hc-row>
				<hc-col :span="12">
					<hc-row >
						<hc-col :span="4"  class="item-content pro-desc">
							<label >
								<text >工单编号:</text>
							</label>
						</hc-col>
						<hc-col :span="8" class="card-value">
							<label >
								<text >{{item.FWORK_ORDER_NO}}</text>
							</label>
						<!-- 	<hc-input style="height:60rpx" type="text" v-model="item.FWORK_ORDER_NO" input-align="left" placeholder=""
										maxlength="500" :disabled="true" :clearable="false"></hc-input> -->
						</hc-col>
					</hc-row>
					<hc-row >
						<hc-col :span="4" class="item-content pro-desc">订单编号:</hc-col>
						<hc-col :span="8" class="card-value">
							<label >
								<text >{{item.FSALE_ORDER_NO}}</text>
							</label>
							<!-- <hc-input style="height:60rpx" type="text" v-model="item.FSALE_ORDER_NO" input-align="left" placeholder=""
										maxlength="500" :disabled="true" :clearable="false"></hc-input> -->
						</hc-col>
					</hc-row>
					<hc-row >
						<hc-col :span="4" class="item-content pro-desc">产品编号:</hc-col>
						<hc-col :span="8" class="card-value">
							<label >
								<text >{{item.FMATERIAL_CODE}}</text>
							</label>
							<!-- <hc-input style="height:60rpx" type="text" v-model="item.FMATERIAL_CODE" input-align="left" placeholder=""
										maxlength="500" :disabled="true" :clearable="false"></hc-input> -->
						</hc-col>
					</hc-row>
					<hc-row >
						<hc-col :span="4" class="item-content pro-desc">产品名称:</hc-col>
						<hc-col :span="8" class="card-value">
							<label >
								<text >{{item.FMATERIAL_NAME}}</text>
							</label>
						<!-- 	<hc-input style="height:60rpx" type="text" v-model="item.FMATERIAL_NAME" input-align="left" placeholder=""
										maxlength="500" :disabled="true" :clearable="false"></hc-input> -->
						</hc-col>
					</hc-row>
					<hc-row >
						<hc-col :span="4" class="item-content pro-desc">生产数量:</hc-col>
						<hc-col :span="2" class="card-value">
							<label >
								<text >{{item.FPRO_QTY}}</text>
							</label>
							<!-- <hc-input style="height:60rpx"  type="text" v-model="item.FPRO_QTY" input-align="left" placeholder=""
										maxlength="500" :disabled="true" :clearable="false"></hc-input> -->
						</hc-col>
						<hc-col :span="4" class="item-content pro-desc">生产单位:</hc-col>
						<hc-col :span="2" class="card-value">
							<label >
								<text >{{item.FPRO_UNIT_NAME}}</text>
							</label>
							<!-- <hc-input style="height:60rpx"  type="text" v-model="item.FPRO_UNIT_NAME" input-align="left" placeholder=""
										maxlength="500" :disabled="true" :clearable="false"></hc-input> -->
						</hc-col>
					</hc-row>
					
				</hc-col>
			</hc-row>
		</view>
		
	</view>
</template>

<script>
	export default {
		name:"hc-select-workOrder",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				
			};
		},
		methods:{
			Tap() {
				this.$emit("click", this.item)
			},
		}
	}
</script>

<style scoped>
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;
	
		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;
	
		}
	
		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				text-align: right;
			}
	
			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}
	
		&-right {
			padding-right: 10rpx;
			text-align: right;
		}
	
		.item-content {
			text-indent: 0;
		}
	}
	.item-content{
		text-align: right;
		margin-bottom:10rpx;
	}
	.Focus {
		color: red;
		font-weight: 900;
	}
	
	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
.card-title{
		text-align: right;
		padding-bottom: 10rpx;
	}
	.card-value{
		text-align: left;
		margin-bottom: 10rpx;
	}
.card-out-box {
		margin: 15rpx 20rpx 15rpx 20rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 15rpx 0;
	}
</style>
