<template>
	<view class="outBox">
		<view class="item" v-if="item" :key="index">
			<view class="item-left">
				<hc-image :src="$hc.Command.GetAttachmentURL(item.FPIC_ATTACH_ID)"></hc-image>
			</view>
			<view style="flex: 1;" class="item-content">
				<hc-row>
					<hc-col :span="12">
						<view class="item-content">
							<view class="desc">
								<view style="width: 100%;">
									<span class="desc-title title-color">名称：</span>
									<span style="color: #000000;">
										{{ item.FSUB_MATERIAL_CODE||"" }}
									</span>
								</view>
							</view>
							<view class="desc">
								<view style="width: 100%;">
									<span class="desc-title title-color">编号：</span>
									<span style="color: #000000;">
										{{ item.FSUB_MATERIAL_NAME||"" }}
									</span>
								</view>
							</view>
							<view class="desc">
								<view style="width: 100%;">
									<span class="desc-title title-color">规格：</span>
									<span style="color: #000000;">
										{{ item.FSUB_SPEC_DESC||"" }}
									</span>
								</view>
							</view>
						</view>
					</hc-col>
				</hc-row>
			</view>
		</view>

		<view class="inputBox">
			<hc-row>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5.5">
							<view class="item-content-type pro-left title-color">单位用量:</view>
						</hc-col>
						<hc-col :span="6.5">
							<hc-input style="height:55rpx;" type="number" v-model="item.FUNIT_QTY"
									input-align="left" placeholder="" maxlength="500" :clearable="false"
									:customStyle="customStyle"
									@blur="FUNIT_QTY_CHANGE(item)"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5">
							<view class="item-content-type pro-left title-color">损耗率:</view>
						</hc-col>
						<hc-col :span="8">
						<hc-input style="height:55rpx;" type="number" v-model="item.FLOSS_RATE"
									input-align="left" placeholder="" maxlength="3" :clearable="false"
									:customStyle="customStyle"
									@blur="FLOSS_RATE_CHANGE(item)"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5.5">
							<view class="item-content-type pro-left title-color">总用量:</view>
						</hc-col>
						<hc-col :span="6.5">
							<hc-input style="height:55rpx;" type="number" v-model="item.FUSE_QTY"
									input-align="left" placeholder="" maxlength="500" :clearable="false"
									:customStyle="customStyle"
									@blur="FUSE_QTY_CHANGE(item)"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5">
							<view class="item-content-type pro-left title-color">单位:</view>
						</hc-col>
						<hc-col :span="8">
							<hc-input style="height:55rpx;" type="text" v-model="item.FSUB_UNIT_NAME"
									input-align="left" placeholder="" maxlength="500" :disabled="true"
									:clearable="false"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5.5">
							<view class="item-content-type pro-left title-color">是否倒扣料:</view>
						</hc-col>
						<hc-col :span="6.5">
							<hc-checkbox style="height:55rpx;" type="text" v-model="item.FIF_REVERSE"
									input-align="left" placeholder=""></hc-checkbox>
						</hc-col>
					</hc-row>
				</hc-col>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5">
							<view class="item-content-type pro-left title-color">更变类型:</view>
						</hc-col>
						<hc-col :span="6">
							<view class="" @tap="updateType(item,index)" :style="customStyle">
								{{FCHANGE_TYPE_NAME}}
								<!-- {{item.FCHANGE_TYPE == 'update'? "编辑" : item.FCHANGE_TYPE == 'add'? "新增" :"删除"}} -->
							</view>
						</hc-col>
					</hc-row>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	export default {
		name: "hc-workOrder-change-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
			mst:{
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					// "font-weight": "bold"
				}
			};
		},
		computed:{
			FCHANGE_TYPE_NAME(e){
				 return this.item.FCHANGE_TYPE == 'update'? "编辑" : this.item.FCHANGE_TYPE == 'add'? "新增" :"删除"
			}
		},
		methods: {
			//更变类型
			updateType(item, index) {
				this.$emit("updateType",{
					item,
					index
				})
				// this.updateTypeList = this[item.FCHANGE_TYPE];
				// this.selectShow = true;
				// this.itemIndex = index;
			},
			
			//==========================================================
			//计算
			//计量栏位用量
			FUNIT_QTY_CHANGE(item) {
				//FUNIT_QTY  计量单位数量
				//mst.FPRO_QTY  生产数量
				//生产数量*单位用量*(1+损耗率%/100)
				//mst.FPRO_QTY * FUNIT_QTY * (1+(FLOSS_RATE/100))
				let {
					FUSE_QTY, //总用量
					FLOSS_RATE, //损耗率
					FUNIT_QTY, //计量单位数量
				} = item;
				const {
					FPRO_QTY
				} = this.mst;
			
				item.FUSE_QTY = this.$hc.Command.Round(FPRO_QTY * FUNIT_QTY * (1 + (FLOSS_RATE / 100)), 6);
				this.calcSTK(item)
			},
			//损耗率 % 
			FLOSS_RATE_CHANGE(item) {
				//FUNIT_QTY  计量单位数量
				//mst.FPRO_QTY  生产数量
				//生产数量*单位用量*(1+损耗率%/100)
				//mst.FPRO_QTY * FUNIT_QTY * (1+(FLOSS_RATE/100))
				// if(item.FLOSS_RATE>100){
			
				// }
				let {
					FUSE_QTY, //总用量
					FLOSS_RATE, //损耗率
					FUNIT_QTY, //计量单位数量
				} = item;
				const {
					FPRO_QTY
				} = this.mst;
				FLOSS_RATE = Number(FLOSS_RATE);
				if (FLOSS_RATE < 0) {
					FLOSS_RATE = 0
				}
				if (FLOSS_RATE > 100) {
					FLOSS_RATE = 100
				}
				item.FUSE_QTY = this.$hc.Command.Round(FPRO_QTY * FUNIT_QTY * (1 + (FLOSS_RATE / 100)), 6);
				item.FLOSS_RATE = FLOSS_RATE
				this.calcSTK(item)
			},
			//总用量
			FUSE_QTY_CHANGE(item) {
				//计量单位用量=总用量/生产数量/(1+损耗率%/100)
				item.FUNIT_QTY = this.$hc.Command.Round(item.FUSE_QTY / this.mst.FPRO_QTY / (1 + (item.FLOSS_RATE / 100)),
					6);
				this.calcSTK(item)
			},
			//计算库存单位
			calcSTK(item) {
				let {
					FUNIT_QTY, //计量单位数量
					FUSE_QTY, //总用量
					FLOSS_RATE, //损耗率
			
					FSTK_USE_QTY, //库存单位总用量
					FUNIT_STK_CONVERT_SCALE //库存单位换算比率
				} = item;
				const {
					FPRO_QTY
				} = this.mst;
				//库存单位用量
				item.FSTK_UNIT_QTY = this.$hc.Command.Round(FUNIT_QTY / FUNIT_STK_CONVERT_SCALE, 6);
				//库存单位总用量
				item.FSTK_USE_QTY = this.$hc.Command.Round((item.FSTK_UNIT_QTY * FPRO_QTY) * (1 + (FLOSS_RATE / 100)), 6);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		padding-bottom: 0;

		// background-color: red;
		border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 10rpx;
			min-width: 160rpx;
			height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 10rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.title {
		font-size: 28rpx;
		line-height: 50rpx;
		font-weight: bold;
		margin: 5rpx 0;
		color: #000000 !important;
	}

	.desc {
		display: flex;
		color: $u-tips-color;
		margin: 10rpx 0;
	}

	.desc view {
		// width: 36%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.desc-title {}

	.pro-left {
		// width: 500rpx;
		font-size: 28rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		text-align: right;
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.inputBox {
		padding-left: 12rpx;
	}

	.outBox {
		border-top: 10rpx #EEF1F5 solid;
		border-bottom: 10rpx #EEF1F5 solid;
		border-left: 15rpx #EEF1F5 solid;
		border-right: 15rpx #EEF1F5 solid;
		// margin: 15rpx 12rpx;
		// display: flex;
	}
	.title-color {
		color: #000000;
	}
</style>
