﻿function downloadFile(url,success,fail)
{
	uni.downloadFile(
	{
		url:url,
		success:(res)=>{
			if (res.statusCode===200) 
			{
				console.log("download success");
			}
			let that=this;
			uni.saveFile({
				tempFilePath:res.tempFilePath,
				success:function(res1){
					//res1.savedFilePath
					if (success)
					{
						success(res1);
					}
				}
			})
		}
	}
	)
}
function downloadFileUseProgress(url,success,fail)
{
	const downloadTask= uni.downloadFile(
	{
		url:url,
		success:(res)=>{
			if (res.statusCode===200) 
			{
				console.log("download success");
			}
			let that=this;
			uni.saveFile({
				tempFilePath:res.tempFilePath,
				success:function(res1){
					//res1.savedFilePath
					if (success)
					{
						success(res1);
					}
				}
			})
		}
	}
	);
	downloadTask.onProgressUpdate((res)=>{
		console.log(res.progress);   //进度
		console.log(res.totalBytesWritten)  //已下载长度
		console.log(res.totalBytesExpectedToWrite) //总长度
		//弹出显示进度条
		
	});
	
	
}
function uploadFile(url,filePath,name,formData,success,fail,complete)
{
	uni.uploadFile(
	{
		url:url,
		filePath:filePath,
		name:name,
		formData:formData,
		header:{"Content-Type":"multipart/form-data"},
		success:(res)=>{
			console.log("res:"+JSON.stringify(res));
			console.log("status:"+res.statusCode);
			if (success)
			{
				success(res);
			}
		},
		fail:(e)=>{
			console.log("网络请求fail");
			if (fail)
			{
				fail(e);
			}
		},
		complete:()=>{
			console.log("网络请求complete");
		}
		
	}
	)
}
export default {
	downloadFile,
	downloadFileUseProgress,
	uploadFile
}
 