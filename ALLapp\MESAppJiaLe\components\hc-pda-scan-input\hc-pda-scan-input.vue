<template>
	<input :value="value" @input="onUpdate"  :placeholder="placeholder" 
	:focus="focus" @blur="onBarblur" confirm-hold="true" confirm-type="search" 
	@confirm="onConfirm" />
</template>
<script>
	/**
	 * 输入框 将pda的扫码广播注册和注销封装在此
	 * 使用说明:
	 * 1.在App.vue中提供注册、注销和回调的方法
	 *    scan:{ 
			callbackfuns:[],
			//注册广播
			registerCallBackFun:function(callback){
				this.callbackfuns.push(callback) ;
			},
			//注销广播
			unRegisterCallBackFun:function(callback){
				var index= this.callbackfuns.findIndex((item)=>{
				 return item==callback;}
				 );
				  
				 this.callbackfuns.splice(index,1);
			},
			//广播模式扫码回调
			 CallBackFun:function(data){ 
				 var index=this.callbackfuns.length;
				 if(index>0)
				 { 
					var callback= this.callbackfuns[index-1];
					callback(data); 
				 }
				 
			}
		2.在App启动后的第一个界面注册广播
		//#ifdef APP-PLUS
		//注册广播
		scanUitls.listenScanStatus(this.$receiver, this.GLOBAL.SCAN_ACTION, (data) => {
			var app = getApp();
			app.globalData.scan.CallBackFun(data);
		});
		//#endif
		3.使用HcInput替代input来实现输入
	 */
export default {
	name: 'hc-pda-scan-input',
	props: {
		/**
		 * 占位符
		 */
		placeholder: String,
		value: {
			type: [String, Number],
			default: ''
		},
		focus: {
			type: [Boolean],
			default: true
		},
		//是否自动填充输入框
		ifAutoFill: {
			type: [Boolean],
			default: true
		}
	},		
	model: {
			prop: 'value',
			event: 'input'
		}, 
	data() {
		return {
			isRegistered: false,//是否已注册
			lastEmit: ''
		}
	},
	created() {
		//注册扫描广播
		var app = getApp();
		app.globalData.scan.registerCallBackFun(this.onScan);
		this.isRegistered = true;
		this.ifAutoFill = true;
	},
	beforeDestroy() {
		//注销扫描广播
		var app = getApp();
		app.globalData.scan.unRegisterCallBackFun(this.onScan);
		this.isRegistered = false;
	},
	methods: {
		onScan(data) {
			if(this.ifAutoFill){
				this.$emit('input', data);
			}
			 console.log('---------confirm by onScan--------')
			this.setEmit('confirm')
		},
		onUpdate(e) {
			this.$emit('input', e.target.value);
		},
		onBarblur() {
			this.$emit('blur');
		},
		onConfirm() {
			// console.log('---------confirm by onConfirm--------')
			this.setEmit('confirm');
		},
		setEmit(thisEmit) {
			if(this.lastEmit != thisEmit) {
				// console.log('---------confirm ready emit--------')
				this.lastEmit = thisEmit;
				this.$emit(thisEmit);
			}
			setTimeout(()=>{
				this.lastEmit = '';
			}, 100)
		},
		registerScan(){
			//注册扫描广播 提供给外部调用（适用场景：一个有需扫码的弹出界面依附在一个需扫码的界面）
			if(this.isRegistered == false){
				var app = getApp();
				app.globalData.scan.registerCallBackFun(this.onScan);
				this.isRegistered = true;
				this.ifAutoFill = true;
			}
		},
		unRegisterScan(){
			//注销扫描广播 提供给外部调用（适用场景：一个有需扫码的弹出界面依附在一个需扫码的界面）
			var app = getApp();
			app.globalData.scan.unRegisterCallBackFun(this.onScan);
			this.isRegistered = false;
		},
		setIfAutoFill(val) {
			this.ifAutoFill = val;
		}	
	}
};
</script>
uni-input { display: block; font-size: 16px; line-height: 1.4em; height: 1.4em; min-height: 1.4em; overflow: hidden; } uni-input[hidden] {
