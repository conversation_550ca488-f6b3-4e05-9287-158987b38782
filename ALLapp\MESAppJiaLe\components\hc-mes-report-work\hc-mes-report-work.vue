<template>
	<view class="item" v-if="item" :key="index" @tap="Tap">
		<!-- <hc-read-more :toggle="read" style="width: 100%;text-indent: 0;" show-height="300" > -->
			<view style="flex: 1;">
				<hc-row>
					<hc-col :span="12">
						<view class="item-content">
							<!-- <hc-row :index="i" v-for="(row, i) in modelList" :key="row.key">
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{row.Name}}：</text>
									</label>
									
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item[row.Key]}}</text>
									</label>
								</hc-col>
							</hc-row> -->
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FWORK_ORDER_NO}}：</text>
									</label>
									
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item.FWORK_ORDER_NO}}</text>
									</label>
								</hc-col>
								
							</hc-row>
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FCRAFT_SCHEDULE_NO}}：</text>
									</label>
								</hc-col>		
								<hc-col :span="8">
									<label >
										<text>{{item.FCRAFT_SCHEDULE_NO}}</text>
									</label>
								</hc-col>
							</hc-row>
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FCRAFT_JOB_BOOKING_NO}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item.FCRAFT_JOB_BOOKING_NO}}</text>
									</label>
								</hc-col>
							</hc-row>
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FMATERIAL}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item.FMATERIAL}}</text>
									</label>
								</hc-col>
							</hc-row>
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FSPEC_DESC}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item.FSPEC_DESC}}</text>
									</label>
								</hc-col>
							</hc-row>
							
							
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FCRAFT}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item.FCRAFT}}</text>
									</label>
								</hc-col>				
							</hc-row>
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FGOODS_MODEL}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item.FGOODS_MODEL}}</text>
									</label>
								</hc-col>
							</hc-row>
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FSTATION}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item.FSTATION}}</text>
									</label>
								</hc-col>							
							</hc-row>
							
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FEMP}}：</text>
									</label>
								</hc-col>			
								<hc-col :span="8">
									<label >
										<text>{{item.FEMP}}</text>
									</label>
								</hc-col>		
							</hc-row>
							
							<hc-row >
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FACT_ST_DATE}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8" class="Focus">
									<label >
										<text>{{item.FACT_ST_DATE}}</text>
									</label>
								</hc-col>	
							</hc-row>
							<hc-row >
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FACT_ED_DATE}}：</text>
									</label>
								</hc-col>
								<hc-col :span="8" class="Focus">
									<label >
										<text>{{item.FACT_ED_DATE}}</text>
									</label>
								</hc-col>
							</hc-row>
							<hc-row >
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FACT_USE_HOUR}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8" class="Focus">
									<label >
										<text>{{item.FACT_USE_HOUR}}</text>
									</label>
								</hc-col>
							</hc-row>
							<hc-row >
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FPASS_QTY}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8" class="Focus">
									<label >
										<text>{{item.FPASS_QTY}} {{item.FPRO_UNIT_NAME}}</text>
									</label>
								</hc-col>
							
							</hc-row>
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FPASS_WEIGHT}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8" class="Focus">
									<label >
										<text>{{item.FPASS_WEIGHT}} {{item.FWEIGHT_UNIT_NAME}}</text>
									</label>
								</hc-col>
							</hc-row>
							<hc-row >
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FNG_QTY}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8" class="Focus">
									<label >
										<text>{{item.FNG_QTY}} {{item.FPRO_UNIT_NAME}}</text>
									</label>
								</hc-col>
								
							</hc-row>
							<hc-row>
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{model.FNG_WEIGHT}}：</text>
									</label>
								</hc-col>	
								<hc-col :span="8" class="Focus">
									<label >
										<text>{{item.FNG_WEIGHT}} {{item.FWEIGHT_UNIT_NAME}}</text>
									</label>
								</hc-col>
							</hc-row>
						</view>
					</hc-col>
				</hc-row>
			</view>
		<!-- </hc-read-more> -->
	</view>
</template>

<script>
	export default {
		name:"hc-mes-report-work",
		props: {
			read:{
				type:Boolean,
				default:true
			},
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				modelList:[
						{
							Name:"工单编号",
							Key:"FWORK_ORDER_NO",
						},
						{
							Name:"排程任务编号",
							Key:"FCRAFT_SCHEDULE_NO",
						},
						{
							Name:"加工任务编号",
							Key:"FCRAFT_JOB_BOOKING_NO",
						},
						{
							Name:"产品",
							Key:"FMATERIAL",
						},
						{
							Name:"规格型号",
							Key:"FSPEC_DESC",
						},
						{
							Name:"工艺",
							Key:"FCRAFT",
						},
						{
							Name:"产品型号",
							Key:"FGOODS_MODEL",
						},
						{
							Name:"工位",
							Key:"FSTATION",
						},
						{
							Name:"报工人员",
							Key:"FEMP",
						},
						{
							Name:"实际开工时间",
							Key:"FACT_ST_DATE",
						},
						{
							Name:"实际完工时间",
							Key:"FACT_ED_DATE",
						},
						{
							Name:"实际工时(小时)",
							Key:"FACT_USE_HOUR",
						},
						// {
						// 	Name:"合格数量",
						// 	Key:"FPASS_QTY",
						// },
						// {
						// 	Name:"合格重量",
						// 	Key:"FPASS_WEIGHT",
						// },
						// {
						// 	Name:"不良数量",
						// 	Key:"FNG_QTY",
						// },
						// {
						// 	Name:"不良重量",
						// 	Key:"FNG_WEIGHT",
						// },
					],
				model:{
					FWORK_ORDER_NO:"工单编号",
					FCRAFT_SCHEDULE_NO:"排程任务编号",
					FCRAFT_JOB_BOOKING_NO:"加工任务编号",
					FMATERIAL:"产品",
					FSPEC_DESC:"规格型号",
					FCRAFT:"工艺",
					FGOODS_MODEL:"产品型号",
					FSTATION:"工位",
					FEMP:"报工人员",
					FACT_ST_DATE:"实际开工时间",
					FACT_ED_DATE:"实际完工时间",
					FACT_USE_HOUR:"实际工时(小时)",
					FPASS_QTY:"合格数量",
					FPASS_WEIGHT:"合格重量",
					FNG_QTY:"不良数量",
					FNG_WEIGHT:"不良重量",
				}
			};
		},
		methods: {
			Tap() {
				this.$emit("click", this.item)
			},
		}
	}
</script>
<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;
			
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				text-align: right;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}
		.item-content{
			text-indent: 0;
		}
	}
	.Focus{
		    color: red;
		    font-weight: 900;
	}
	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>