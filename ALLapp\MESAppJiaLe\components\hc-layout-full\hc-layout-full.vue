﻿<template>
	<view class="hc-layout-full">
		<scroll-view :enable-back-to-top="enableBackToTop" v-if="height > 0" scroll-y :refresher-enabled="enabledRefresher"
		 :refresher-triggered="triggered" @refresherrefresh="refresherrefresh" @scrolltolower="scrolltolower"
		 :lower-threshold="lowerThreshold" :style="{height: height+'px'}">
			<slot v-if="!showEmpty"></slot>
			<hc-empty v-if="showEmpty" :mode="emptyMode" />
			<hc-loadmore v-else-if="showBottom" :status="bottomStatus" />
		</scroll-view>
	</view>
</template>

<script>
	/**
	 * lazyLoad 布局容器
	 * @description 高度自适应 滚动区域 下拉刷新 加载更多
	 * @property {Boolean} enableBackToTop iOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只支持竖向
	 * @property {Boolean} enabledRefresher 开启下拉刷新功能
	 * @property {Number} lowerThreshold 距底部/右边多远时（单位px），触发 scrolltolower 事件
	 * @property {Boolean} showBottom 底部状态是否显示
	 * @property {String} bottomStatus 底部状态
	 * @property {Boolean} showEmpty 显示为空页
	 * @property {String} emptyMode 空页mode
	 * @event {Function} ready 高度获取成功后执行 此事件回调内 可以 执行表单setRules操作
	 * @example <hc-layout-full @ready="readyHandle"></hc-layout-full>
	 */
	export default {
		name: "hc-layout-full",
		inject: ["layout"],
		data() {
			return {
				height: 0,
				_refreshing: false,
				triggered: false
			};
		},
		props: {
			// iOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只支持竖向
			enableBackToTop: {
				type: Boolean,
				default: true
			},
			// 开启下拉刷新功能
			enabledRefresher: {
				type: Boolean,
				default: false
			},
			// 距底部/右边多远时（单位px），触发 scrolltolower 事件
			lowerThreshold: {
				type: Number,
				default: 50
			},
			// 底部状态是否显示
			showBottom: {
				type: Boolean,
				default: true
			},
			// 底部状态
			bottomStatus: {
				type: String,
				default: "nomore"
			},
			//显示为空页
			showEmpty: {
				type: Boolean,
				default: false
			},
			//空页mode
			emptyMode: {
				type: String,
				default: "list"
			}
		},
		watch: {
			"layout.height": function(newVal) {
				const that = this;
				// 修复 App端 高度获取为0的问题
				// #ifdef APP-PLUS
				setTimeout(() => {
					uni.createSelectorQuery().in(this).select(".hc-layout-full").boundingClientRect(res => {
						that.height = res.height;
					}).exec();
				}, 100)
				// #endif
				// #ifndef APP-PLUS
				uni.createSelectorQuery().in(this).select(".hc-layout-full").boundingClientRect(res => {
					that.height = res.height;
				}).exec();
				// #endif
			},
			height(newVal) {
				if (newVal > 0 && !this.hadReady) {
					this.$nextTick(() => {
						this.hadReady = true;
						this.$emit("ready");
					})
				}
			}
		},
		methods: {
			refresherrefresh() {
				if (this._refreshing) return
				this._refreshing = true;
				if (!this.triggered) this.triggered = true;
				this.$emit("refresh");
			},
			scrolltolower() {
				this.$emit("scrolltolower");
			},
			restore() {
				this._refreshing = false;
				this.triggered = false;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-layout-full {
		/* #ifdef MP-WEIXIN */
		height: 100%;
		/* #endif */
		/* #ifndef MP-WEIXIN */
		flex: 1;
		/* #endif */
		overflow: hidden;
	}
</style>
