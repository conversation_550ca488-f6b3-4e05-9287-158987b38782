<template>
	<view class="box" style="" v-if="item" :key="index">
		<hc-row>
			<hc-col :span="6">
				<view class="item-content-title pro-left">仓库：{{ item.FSTORE_NAME||"" }}</view>
			</hc-col>
			<hc-col :span="6">
				<view class="item-content-type pro-left">货位：{{ item.FSTORE_PLACE_NAME||"" }}</view>
			</hc-col>
		</hc-row>
		<hc-row>
			<hc-col :span="6">
				<view class="item-content-type pro-left">单位：{{ item.FSTK_UNIT_NAME||"" }}</view>
			</hc-col>
			<hc-col :span="6">
				<view class="item-content-type pro-left">现存量：{{ item.FSTK_UNIT_QTY||0 }}</view>
			</hc-col>
		</hc-row>
	</view>
</template>

<script>
	/*
	 * @example <hc-material-recv-card item="{}" index="0"></hc-material-recv-card>
	 */
	export default {
		name: "hc-store-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
			editData: {
				type: Object,
				default () {
					return {
						title: "",
						field: '',
						// fieldQty:'',
					}
				}
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				}
			};
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			},

			// change(value) {
			// 	debugger
			// 	console.log("---value", value)
			// 	// this.item.FSTOCKING_QTY = value
			// 	this.$emit("ChildChange", value)
			// }
		}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;

		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 20rpx;
			min-width: 200rpx;
			max-width: 200rpx;
			height: 200rpx;
		}

		&-content {
			&-title {
				font-size: 32rpx;
				line-height: 50rpx;
				font-weight: bold;
			}

			&-type {
				margin: 10rpx 0;
				font-size: 30rpx;
				// font-weight: bold;
				// color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.box {
		padding: 12rpx;
		margin:6rpx 12rpx ;
		border-radius: 10rpx;
		background-color: #FFFFFF;
	}

	.pro-left {
		// width: 500rpx;
		//width: calc(100%/12);
		// width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.pro-right {
		// width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
