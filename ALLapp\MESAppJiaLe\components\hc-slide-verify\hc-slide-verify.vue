﻿<template>
	<view class="hc-slide-verify" v-show="config.Key" :style="{width: config.ImgX + 'px'}" ref="validateWrap">
		<view class="hc-slide-verify-content" :style="{height: config.ImgY + 'px'}">
			<view class="hc-slide-verify-content-item" v-for="item in list" :style="{width: getItemWidth(), height: getItemHeight(), backgroundImage: getItemBackgroundImage(), backgroundPosition: getItemBackgroundPosition(item)}"
			 :key="item"></view>
			<view class="hc-slide-verify-content-block" :style="blockStyle"></view>
		</view>
		<movable-area class="hc-slide-verify-operate" :animation="true">
			<movable-view class="hc-slide-verify-operate-toggle" :x="moveX" direction="horizontal" @change="moveChange">
				<view class="hc-slide-verify-operate-toggle-content" @touchend="moveEnd" @touchstart="moveStart">
					<hc-icon name="arrow-right-double"></hc-icon>
				</view>
			</movable-view>
			<view class="hc-slide-verify-operate-placeholder">拖动滑块验证</view>
			<view class="hc-slide-verify-operate-bg" :style="{width: oldX + 'px', backgroundColor: $themeCurrent.main}"></view>
			<view class="hc-slide-verify-operate-reload" v-show="!isMoving" @click="refresh">
				<hc-icon name="reload" size="36"></hc-icon>
			</view>
		</movable-area>
	</view>
</template>

<script>
	/**
	 * slideVerify 滑块验证
	 * @description 登录注册等进行防刷验证
	 * @example <hc-slide-verify @success="" @fail=""></hc-slide-verify>
	 */
	let record = [];
	let startTimestamp = null;
	export default {
		name: "hc-slide-verify",
		data() {
			return {
				list: Array.from(Array(20)).map((item, index) => index),
				config: {},
				isMoving: false,
				moveX: 0,
				oldX: 0,
			};
		},
		computed: {
			blockStyle() {
				const {
					Y,
					Small
				} = this.config;
				let result = ["width: 40px", "height: 40px", `left: ${this.oldX}px`];
				if (Y) result.push(`top: ${Y}px`);
				if (Small) result.push(`background-image: url(${Small})`);
				return result.join(";");
			}
		},
		methods: {
			moveStart() {
				this.isMoving = true;
				startTimestamp = new Date();
			},
			moveChange(e) {
				if (this.isMoving) {
					const {
						x
					} = e.detail;
					record.push([x, new Date().getTime()]);
					this.oldX = x;
				}
			},
			getCode() {
				this.$hc.request({
						url: "/api/EOS006VerifyCode/GetVerificationCodeAsync",
						disableLoading: true
					})
					.then((res) => {
						const {
							Entity
						} = res;
						if (Entity.ErrCode === 0) {
							this.config = Entity;
						}
					});
			},
			refresh() {
				this.moveX = this.oldX;
				this.$nextTick(() => {
					this.moveX = 0;
					this.oldX = 0;
					this.getCode();
				});
			},
			indexOf(arr, str) {
				if (arr && arr.indexOf) return arr.indexOf(str);
				let len = arr.length;
				for (let i = 0; i < len; i++) {
					if (arr[i] === str) return i;
				}
				return -1;
			},
			getItemWidth() {
				const {
					ImgX
				} = this.config;
				return ImgX / 10 + "px"
			},
			getItemHeight() {
				const {
					ImgY
				} = this.config;
				return ImgY / 2 + "px"
			},
			getItemBackgroundImage() {
				const {
					Normal
				} = this.config;
				return `url(${Normal})`
			},
			getItemBackgroundPosition(index) {
				const {
					ImgX,
					ImgY,
					ImgArray: ImgArrayStr
				} = this.config;
				if (ImgArrayStr) {
					// 还原图片
					var bgarray = ImgArrayStr.split(",");
					var _cutX = ImgX / 10;
					var _cutY = ImgY / 2;
					var num = this.indexOf(bgarray, index.toString()); // 第i张图相对于混淆图片的位置为num
					var x = 0,
						y = 0;
					// 还原前偏移
					y = index > 9 ? -_cutY : 0;
					x = index > 9 ? (index - 10) * -_cutX : index * -_cutX;
					// 当前y轴偏移量
					if (num > 9 && index < 10) y = y - _cutY;
					if (index > 9 && num < 10) y = y + _cutY;
					// 当前x轴偏移量
					x = x + (num - index) * -_cutX;
					return `${x}px ${y}px`
				}
			},
			moveEnd() {
				this.isMoving = false;
				let now = new Date();
				let UsedTime = now - startTimestamp;
				const {
					Key
				} = this.config;
				this.$hc.request({
						url: "/api/EOS006VerifyCode/CheckCodeAsync",
						disableLoading: true,
						data: {
							model: {
								PointX: this.oldX,
								SlideRecord: record.join("|"),
								UsedTime: UsedTime,
								Key: Key,
							},
						},
					})
					.then((res) => {
						const {
							StatusCode
						} = res;
						if (StatusCode === 200) {
							record = [];
							this.$emit("success", Key);
						} else {
							record = [];
							this.refresh();
							this.$emit("fail", Key);
						}
					})
					.catch(() => {
						record = [];
						this.refresh();
						this.$emit("fail", Key);
					});
			},
		},
		created() {
			this.getCode();
		}
	}
</script>

<style lang="scss" scoped>
	$bg-color: #7ac23c;

	.hc-slide-verify {
		margin: 0 auto;

		/* 图片展示区域 */
		&-content {
			position: relative;
			// background-color: #ddd;

			&-item {
				display: inline-block;
				vertical-align: top;
			}

			&-block {
				position: absolute;
				box-shadow: 0 0 15px #0cc;
				border: 1px solid #fff;
			}
		}

		&-operate {
			width: 100%;
			height: 40px;
			line-height: 40px;
			background-color: $uni-bg-color-grey;
			border-top: 1px solid $uni-border-color;

			&-toggle {
				z-index: 999;
				width: 40px;
				height: 100%;

				&-content {
					width: 100%;
					height: 100%;
					line-height: 39px;
					background-color: #fff;
					text-align: center;
					border-right: 1px solid $uni-border-color;
					border-bottom: 1px solid $uni-border-color;
					border-left: 1px solid $uni-border-color;
					cursor: pointer;
				}
			}

			&-bg {
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0;
			}

			&-placeholder {
				position: absolute;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				text-align: center;
				height: 100%;
				font-size: 32rpx;
			}

			&-reload {
				position: absolute;
				top: 0;
				right: 0;
				bottom: 0;
				height: 100%;
				width: 30px;
				text-align: center;
			}
		}
	}
</style>
