import requestConfig from '../../request/config.js';
import request from '../../request/request.js';
import api from '../../request/api.js';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import i18n from '@/i18n/index.js';
import dateStrFormat from '../function/dateStrFormat.js';
import pageUrl from '../../request/pageUrl.js';
import themeColors from './themeColors.js';
import {
	GetThemeIndex,
	GetUUID,
	GetMenuRights
} from './storage.js';
dayjs.extend(duration);
const defaultThemeIndex = requestConfig.defaultThemeIndex;

function getThemeColor() {
	let themeIndex = GetThemeIndex();
	if (!themeIndex) {
		themeIndex = defaultThemeIndex
	} else {
		themeIndex = Number(themeIndex)
		if (isNaN(themeIndex) || themeIndex < 0 || themeIndex > themeColors.length) {
			themeIndex = defaultThemeIndex;
		}
	}
	return themeColors[themeIndex]["main"]
}
export function getThemeIndex() {
	let themeIndex = GetThemeIndex();
	if (!themeIndex) {
		themeIndex = defaultThemeIndex
	} else {
		themeIndex = Number(themeIndex)
		if (isNaN(themeIndex) || themeIndex < 0 || themeIndex > themeColors.length) {
			themeIndex = defaultThemeIndex
		}
	}
	return themeIndex
}

/**
 * 根据附件Id返回附件的url
 * @param {any} id
 */
export function GetAttachmentURL(id, useQuery = true) {
	if (IsEmpty(id)) {
		return "";
	} else {
		return useQuery ?
			`${requestConfig.baseUrl}/api/attachmentup/DownFileAsync?id=${encodeURIComponent(id)}` :
			`${requestConfig.baseUrl}/api/attachmentup/DownFileWithIdAsync/${encodeURIComponent(id)}`;
	}
};

/**
 * 返回api下载文件的url(带上token)
 * @param {any} api
 */
export function GetFileDataURL(api) {
	var url = requestConfig.baseUrl + api;
	if (url.indexOf("?") > -1) {
		url = url + "&token=" + encodeURIComponent(GetUUID());
	} else {
		url = url + "?token=" + encodeURIComponent(GetUUID());
	}
	return url;
};

/**
 * 是否String类型
 * @param {*} v
 * @returns Boolean
 */
export function IsString(v) {
	return Object.prototype.toString.call(v) === "[object String]";
};
/**
 * 是否Number类型
 * @param {*} v
 * @returns Boolean
 */
export function IsNumber(v) {
	return Object.prototype.toString.call(v) === "[object Number]";
};

/**
 * 验证是否整数
 * @param {any} v
 */
export function IsInt(v) {
	var regexNum = /^\d+$/; //整数
	return regexNum.test(v);
};

/**
 * 是否Boolean类型
 * @param {*} v
 * @returns Boolean
 */
export function IsBoolean(v) {
	return Object.prototype.toString.call(v) === "[object Boolean]";
};
/**
 * 是否Function类型
 * @param {*} v
 * @returns Boolean
 */
export function IsFunction(v) {
	return Object.prototype.toString.call(v) === "[object Function]";
};
/**
 * 是否为null
 * @param {*} v
 * @returns Boolean
 */
export function IsNull(v) {
	return Object.prototype.toString.call(v) === "[object Null]";
};
/**
 * 是否
 * @param {*} v
 * @returns Boolean
 */
export function IsObject(v) {
	return Object.prototype.toString.call(v) === "[object Object]";
};
/**
 * 是否为Undefined
 * @param {*} v
 * @returns Boolean
 */
export function IsUndefined(v) {
	return Object.prototype.toString.call(v) === "[object Undefined]";
};
/**
 * 是否Array类型
 * @param {*} v
 * @returns Boolean
 */
export function IsArray(v) {
	return Object.prototype.toString.call(v) === "[object Array]";
};
/**
 * 是否Date类型
 * @param {*} v
 * @returns Boolean
 */
export function IsDate(v) {
	return Object.prototype.toString.call(v) === "[object Date]";
};
/**
 * 是否RegExp类型
 * @param {*} v
 * @returns Boolean
 */
export function IsRegExp(v) {
	return Object.prototype.toString.call(v) === "[object RegExp]";
};
/**
 * 是否为空 为 null undefined 空字符串 成立
 * @param {*} v
 * @returns Boolean
 */
export function IsEmpty(v) {
	return !v && v !== false && v !== 0;
};

/**
 * 错误输出
 * @description（开发环境下）用于 方法 组件 内部 参数类型错误 不规范写法 的提示
 * @param {String} str 
 */
export function ErrorLog(str) {
	if (process.env.NODE_ENV !== "production") {
		console.warn(`hc平台提示：${str}`)
	}
}

/**
 * 对象转换成 url QueryString
 * @description 对象转换成 url QueryString 可携带在url上
 * @param {Object} obj
 * @return {String}
 */
export function ObjectConvertQueryString(obj) {
	if (IsObject(obj)) {
		return Object.keys(obj).map(key => {
			let value = obj[key];
			if (IsObject(value) || IsArray(value)) {
				value = encodeURIComponent(JSON.stringify(value))
			}
			return `${key}=${value}`;
		}).join("&");
	} else {
		ErrorLog("指令ObjectConvertQueryString参数应为undefined或Object类型");
		return "";
	}
}


/**
 * 序列化
 * @param {Object} obj
 * @returns String
 */
export function Serialize(obj) {
	return JSON.stringify(obj);
};
/**
 * 反序列化
 * @param {String} str
 * @returns Object
 */
export function DeSerialize(str) {
	return JSON.parse(str);
};
/**
 * 设定一次性定时器
 * @param {any} callback 回调
 * @param {any} delay 间隔时间
 * @param {any} rest param1, param2, ..., paramN 等附加参数，它们会作为参数传递给回调函数
 */
export function SetTimeout(callback, delay, rest) {
	setTimeout(callback, delay, rest);
};
/**
 * 取消一次性定时器
 * @param {any} timeoutID
 */
export function ClearTimeout(timeoutID) {
	clearTimeout(timeoutID);
};
/**
 * 周期性定时器
 * @param {any} callback 回调
 * @param {any} delay 间隔时间
 * @param {any} rest param1, param2, ..., paramN 等附加参数，它们会作为参数传递给回调函数
 */
export function SetInterval(callback, delay, rest) {
	setInterval(callback, delay, rest);
};
/**
 * 取消周期性定时器
 * @param {any} intervalID
 */
export function ClearInterval(intervalID) {
	clearInterval(intervalID);
};
// 同 C# String.format
export function StringFormat() {
	if (arguments.length == 0) return null;
	var str = arguments[0];
	for (var i = 1; i < arguments.length; i++) {
		var re = new RegExp("\\{" + (i - 1) + "\\}", "gm");
		str = str.replace(re, arguments[i]);
	}
	return str;
};
/**
 * 加载数据
 * @param {any} url
 * @param {any} data
 * @param {any} successFun
 * @param {any} ops
 */
export function LoadData(url, data, successFun, ops) {
	ops = ops || {};
	return request.request({
		disableLoading: ops.disableLoading,
		forbidToken: !!ops.forbidToken,
		url: url,
		data: data,
	}).then(res => {
		IsFunction(successFun) && successFun(res);
	}).catch(err => {
		IsFunction(ops.ErrFun) && ops.ErrFun(err);
	})
};
/**
 * 加载数据 默认不转圈 用法同LoadData
 * @param {any} url
 * @param {any} data
 * @param {any} successFun
 * @param {any} ops
 */
export function QueryData(url, data, successFun, ops) {
	ops = ops || {};
	return request.request({
		disableLoading: IsBoolean(ops.disableLoading) ? ops.disableLoading : true,
		forbidToken: !!ops.forbidToken,
		url: url,
		data: data,
	}).then(res => {
		IsFunction(successFun) && successFun(res);
	}).catch(err => {
		IsFunction(ops.ErrFun) && ops.ErrFun(err);
	})
};

/**
 * 模态弹窗 确认
 */
export function ConfirmDialog(ops) {
	ops = ops || {};
	if (IsEmpty(ops.title)) {
		ops.title = i18n.t("TIPS");
	}
	uni.showModal({
		title: ops.title,
		confirmColor: getThemeColor(),
		content: ops.content,
		success: function(res) {
			if (res.confirm) {
				IsFunction(ops.confirm) && ops.confirm();
			} else if (res.cancel) {
				IsFunction(ops.cancel) && ops.cancel();
			}
		}
	});
};
/**
 * 模态弹窗 询问
 */
export function AskDialog(ops) {
	ops = ops || {};
	if (IsEmpty(ops.title)) {
		ops.title = i18n.t("INQUIRY");
	}
	uni.showModal({
		title: ops.title,
		content: ops.content,
		confirmColor: getThemeColor(),
		success: function(res) {
			if (res.confirm) {
				IsFunction(ops.confirm) && ops.confirm();
			} else if (res.cancel) {
				IsFunction(ops.cancel) && ops.cancel();
			}
		}
	});
};
/**
 * 模态弹窗 提示
 */
export function NotifyDialog(ops) {
	ops = ops || {};
	if (IsEmpty(ops.title)) {
		ops.title = i18n.t("TIPS");
	}
	uni.showModal({
		title: ops.title,
		content: ops.content,
		confirmColor: getThemeColor(),
		showCancel: false,
		success: function(res) {
			if (res.confirm) {
				IsFunction(ops.confirm) && ops.confirm();
			}
		}
	});
};
/**
 * 模态弹窗 警告
 */
export function WarnDialog(ops) {
	ops = ops || {};
	if (IsEmpty(ops.title)) {
		ops.title = i18n.t("WARN");
	}
	uni.showModal({
		title: ops.title,
		content: ops.content,
		confirmColor: getThemeColor(),
		showCancel: false,
		success: function(res) {
			if (res.confirm) {
				IsFunction(ops.confirm) && ops.confirm();
			}
		}
	});
};

/*
根据请求状态码, 发出警告/异常提示
*/
export function ShowResMessage(res) {
	if (res.StatusCode >= 100000 && res.StatusCode < 200000) {
		WarnDialog({
			content: res.Message
		});
	} else {
		ExceptionDialog({
			content: res.Message
		});
	}
};

/**
 * toast 轻提示
 * @param {Object}  
 * @description duration 默认2000
 */
export const ShowToast = (options) => uni.showToast(Object.assign({
	duration: 2000
}, options));
/**
 * 获取平台支持的语言
 * @param {any}
 * @returns
 */
export function GetSupportLangs() {
	return {
		CN: {
			locale: "zh-cn",
			index: "0"
		},
		EN: {
			locale: "en",
			index: "1"
		},
		VI: {
			locale: "vi",
			index: "2"
		},
		CHT: {
			locale: "zh-tw",
			index: "3"
		},
	};
};
/*
 * 把传入的日期值格式化成 YYYY-MM-DD
 **/
export function FormatDate(dateVal) {
	if (dateVal) {
		return dayjs(dateStrFormat(dateVal)).format("YYYY-MM-DD");
	}
	return dateVal || "";
};

/*
 * 把传入的日期值格式化成 YYYY-MM-DD HH:mm:ss
 **/
export function FormatDateTime(dateVal) {
	if (dateVal) {
		return dayjs(dateStrFormat(dateVal)).format("YYYY-MM-DD HH:mm:ss");
	}
	return dateVal || "";
};


/*
 * 把传入的日期值格式化成 MM-DD
 **/
export function FormatMonDate(dateVal) {
	if (dateVal) {
		return dayjs(dateStrFormat(dateVal)).format("MM-DD");
	}
	return dateVal || "";
};


/**
 * 把传入的时长数格式化成 HH:mm:ss
 * @param {时长} interval
 * @param {单位} unit
 */
export function FormatHour(
	interval, unit
) {
	var durationData = dayjs.duration(interval, unit);
	var hours = (durationData.get("years") * 366 * 24 + durationData.get("months") * 31 * 24 +
		durationData.get("days") * 24 + durationData.get("hours")).toString();
	if (hours.length < 2) {
		hours = '0' + hours;
	}

	var minutes = durationData.get("minutes").toString();
	if (minutes.length < 2) {
		minutes = '0' + minutes;
	}

	var seconds = durationData.get("seconds").toString();
	if (seconds.length < 2) {
		seconds = '0' + seconds;
	}

	return hours + ':' + minutes + ':' + seconds;
};

/*
 * 时间比大小，dateVal1 不传，与当前时间比  相当于 dateVal1<dateVal2
 **/
export function DateIsBefore(dateVal1, dateVal2) {
	return dayjs(dateStrFormat(dateVal1)).isBefore(dayjs(dateStrFormat(dateVal2)));
};

/*
 * 传入日期 减去天数
 **/
export function SubtractDay(date, day) {
	return dayjs(date).subtract(Number(day), 'day')
};



/**
 * 保存前提示
 * @param {any} ops
 */
export function SaveConfirm(ops) {
	ops = ops || {};
	if (IsEmpty(ops.title)) {
		ops.title = i18n.t("TIPS");
	}
	if (IsEmpty(ops.content)) {
		ops.content = i18n.t("CONFIRM_SAVE");
	}
	return ConfirmDialog(ops);
};

/**
 * 取消前提示
 * @param {any} yes
 */
export function UndoConfirm(ops) {
	ops = ops || {};
	if (IsEmpty(ops.title)) {
		ops.title = i18n.t("TIPS");
	}
	if (IsEmpty(ops.content)) {
		ops.content = i18n.t("CONFIRM_CANCEL_OPERATION");
	}
	return ConfirmDialog(ops);
};

/** * 在编辑状态下删除前提示
 * @param {any} yes
 */
export function DeleteConfirmInEdit(ops) {
	ops = ops || {};
	if (IsEmpty(ops.title)) {
		ops.title = i18n.t("TIPS");
	}
	if (IsEmpty(ops.content)) {
		ops.content = i18n.t("CONFIRM_DELETE_DATA");
	}
	return ConfirmDialog(ops);

};

/**
 * 删除前提醒
 * @param {any} url
 * @param {any} data
 * @param {any} SuccessFun
 */
export function DeleteConfirm(url, data, SuccessFun) {
	ConfirmDialog({
		title: i18n.t("TIPS"),
		content: i18n.t("CONFIRM_DELETE_DATA"),
		confirm: () => {

			request.request({
				url: url,
				data: data,
			}).then(res => {
				IsFunction(SuccessFun) && SuccessFun(res);
			}).catch(err => {
				ExceptionDialog({
					content: err.Message,
				});
			});
		}
	}, );
};

/**
 * 异常提示
 */
export function ExceptionDialog(ops) {
	ops = ops || {};
	if (IsArray(ops.content)) {
		ops.content = ops.content.join("\r\n");
	}
	uni.showModal({
		title: i18n.t("ERROR"),
		content: i18n.t("HCloud_TIPS") + "：" + i18n.t("SYSTEM_HAS_ERROR") + "!",
		cancelText: i18n.t('View_DETAIL'),
		confirmText: i18n.t("CLOSE"),
		confirmColor: getThemeColor(),
		success: function(res) {
			if (res.cancel) {
				//查看详情
				uni.showModal({
					title: i18n.t("ERROR"),
					content: ops.content,
					confirmColor: getThemeColor(),
					confirmText: i18n.t("CLOSE"),
					showCancel: false,
				});
			}
		}
	});

};
/**
 * 对数字进行四舍五入计算
 * @param {*} num 数字
 * @param {*} fixed 小数位
 */
export function Round(num, fixed) {
	return Math.round(num * Math.pow(10, fixed)) / Math.pow(10, fixed);
};
/**
 * 截取两位有效数字
 */
export function MathFixedFun(num) {
	if (isNaN(num)) {
		return num;
	} else {
		//return parseFloat(num).toFixed(2);
		return Math.round(num * 100) / 100;
	}
};
/**
 * 科学计数法
 */
export function formatAmount(num) {
	if (!num) {
		return 0;
	}
	var num_top = "";
	var num_tail = "";
	var result = '';
	var re = new RegExp("^(-?\\d+)(\\.\\d+)$"); //判断是否是浮点数 

	if (re.test(num)) {
		var strSum = new String(num);
		if (strSum.indexOf(".") > -1) {
			num_tail = strSum.split(".")[1];
			num_top = strSum.split(".")[0];
		}
		while (num_top.length > 3) {
			result = ',' + num_top.slice(-3) + result;
			num_top = num_top.slice(0, num_top.length - 3);
		}
		if (num_top) {
			result = num_top + result + '.' + num_tail;
		}
	} else {
		num_top = new String(num);
		while (num_top.length > 3) {
			result = ',' + num_top.slice(-3) + result;
			num_top = num_top.slice(0, num_top.length - 3);
		}
		if (num_top) {
			result = num_top + result;
		}
	}
	return result;
};
/**
 * 获取Guid
 */
export function GetGuid() {
	return "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g, function(c) {
		var r = (Math.random() * 16) | 0,
			v = c == "x" ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
};

/**
 * 路由跳转 普通页面
 * @description 保留当前页面，跳转到应用内的某个页面
 * @param {String} url 页面路径
 * @param {String, Object} query url携带参数
 * @param {Object} ops 其他 uni.navigateTo 配置选项
 * @param {String} title 点击得菜单名称 
 */
export function NavigateToByUrl(url, query, ops, title) {
	if (url) {
		// 处理参数
		if (IsObject(query)) {
			url += `?${ObjectConvertQueryString(query)}`;
		} else if (IsString(query)) {
			url += `?${query}`;
		}
		//判断是否阿里BI的路径
		if (url.indexOf("bi.aliyuncs.com") != -1) {
			// 传长字符串需要编码encodeURIComponent(JSON.stringify())
			let frameUrl = encodeURIComponent(JSON.stringify(url))
			let frameTitle = encodeURIComponent(JSON.stringify(title))
			uni.navigateTo(Object.assign({}, ops, {
				url: "/pages/MainPackage/AliyunBI?frameUrl=" + frameUrl + "&frameTitle=" + frameTitle
			}));
		} else {
			uni.navigateTo(Object.assign({}, ops, {
				url,
				fail: (res) => {
					console.log(res) //打印错误信息
				}
			}));
		}

	} else {
		ErrorLog("页面url错误");
	}
};

/**
 * 路由跳转 普通页面
 * @description 保留当前页面，跳转到应用内的某个页面
 * @param {String} urlName 页面名称
 * @param {String, Object} query url携带参数
 * @param {Object} ops 其他 uni.navigateTo 配置选项
 */
export function NavigateTo(urlName, query, ops) {
	let pageInfo = pageUrl[urlName];
	if (pageInfo) {
		let url = pageInfo.url;
		// 处理参数
		if (IsObject(query)) {
			url += `?${ObjectConvertQueryString(query)}`;
		} else if (IsString(query)) {
			url += `?${query}`;
		}
		uni.navigateTo(Object.assign({}, ops, {
			url
		}));
	} else {
		ErrorLog("指令NavigateTo未匹配到页面");
	}
};

/**
 * 路由跳转 tabBar 页面
 * @description 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
 * @param {String} urlName 页面名称
 * @param {String, Object} query url携带参数 （跳转tabBar页面 url后可携带参数 只是取不到）
 * @param {Object} ops 其他 uni.switchTab 配置选项
 */
export function SwitchTab(urlName, query, ops) {
	let pageInfo = pageUrl[urlName];
	if (pageInfo) {
		let url = pageInfo.url;
		// 处理参数
		if (IsObject(query)) {
			url += `?${ObjectConvertQueryString(query)}`;
		} else if (IsString(query)) {
			url += `?${query}`;
		}
		uni.switchTab(Object.assign({}, ops, {
			url
		}));
	} else {
		ErrorLog("指令SwitchTab未匹配到页面");
	}
};

/**
 * 路由重定向
 * @description 关闭当前页面，跳转到应用内的某个页面
 * @param {String} urlName 页面名称
 * @param {String, Object} query url携带参数
 * @param {Object} ops 其他 uni.redirectTo 配置选项
 */
export function RedirectTo(urlName, query, ops) {
	let pageInfo = pageUrl[urlName];
	if (pageInfo) {
		let url = pageInfo.url;
		// 处理参数
		if (IsObject(query)) {
			url += `?${ObjectConvertQueryString(query)}`;
		} else if (IsString(query)) {
			url += `?${query}`;
		}
		uni.redirectTo(Object.assign({}, ops, {
			url
		}));
	} else {
		ErrorLog("指令RedirectTo未匹配到页面");
	}
};

/**
 * 路由重置
 * @description 关闭所有页面，打开到应用内的某个页面
 * @param {String} urlName 页面名称
 * @param {String, Object} query url携带参数
 * @param {Object} ops 其他 uni.reLaunch 配置选项
 */
export function ReLaunch(urlName, query, ops) {
	let pageInfo = pageUrl[urlName];
	if (pageInfo) {
		let url = pageInfo.url;
		// 处理参数
		if (IsObject(query)) {
			url += `?${ObjectConvertQueryString(query)}`;
		} else if (IsString(query)) {
			url += `?${query}`;
		}
		uni.reLaunch(Object.assign({}, ops, {
			url
		}));
	} else {
		ErrorLog("指令ReLaunch未匹配到页面");
	}
};
/**
 * 路由回退
 * @description 关闭当前页面，返回上一页面或多级页面
 * @param {Object} ops 配置选项 同 uni.navigateBack
 */
export function NavigateBack(ops) {
	if (IsObject(ops) || ops === undefined) {
		uni.navigateBack(Object.assign({
			delta: 1
		}, ops));
	} else {
		ErrorLog("指令NavigateBack参数应为undefined或Object类型");
	}
};

/**
 * 路由跳转 (不区分普通页面 | tabBar页面)
 * @description 保留当前页面，跳转到应用内的某个页面
 * @param {String} urlName 页面名称
 * @param {String, Object} query url携带参数
 * @param {Object} ops 其他配置选项
 */
export function RouteTo(urlName, query, ops) {
	let pageInfo = pageUrl[urlName];
	if (pageInfo) {
		let {
			url,
			isTabBar
		} = pageInfo;
		if (isTabBar) {
			SwitchTab(urlName, query, ops);
		} else {
			NavigateTo(urlName, query, ops);
		}
	} else {
		ErrorLog("指令RouteTo未匹配到页面");
	}
};

/**
 * url参数解码+反序列化
 * @description 
 * @param {String} str 
 */
export function DecodeAndParseUrlQuery(str) {
	let result;
	try {
		result = JSON.parse(decodeURIComponent(str));
	} catch (e) {}
	return result;
}

/**
 * 显示 loading 提示框
 * @showLoading 
 * @param {Object} opts 
 */
export function ShowLoading(opts) {
	uni.showLoading(opts);
}


/**
 * 隐藏 loading 提示框
 * @hideLoading 
 */
export function HideLoading() {
	uni.hideLoading();
}

/**
 * 保存成功跳转
 * @description 保存成功跳转，默认关闭当前页面
 * @param {Object} ops 保存成功配置选项 
 * @param {String} redirectType 重定向方式
 */
export function ToSaveSuccessPage(ops, redirectType = "RedirectTo") {
	this[redirectType]("SaveSuccessAppPage", {
		options: ops
	});
};


/**
 * 将二维码字符串解析为对象
 * @hideLoading 
 */
export function ParseQRCodeStr(str) {
	var reg = /([^=&\s]+)[=\s]*([^&\s]*)/g;
	var qrCodeObj = {};
	while (reg.exec(str)) {
		qrCodeObj[RegExp.$1] = RegExp.$2;
	};
	return qrCodeObj || {};
};

/**
 * 附件上传
 * @param {String} filePath  文件路径
 * @param {String} name  上传字段名 可空
 * @param {Object} config  通过typeCode从接口获取的配置  
 * @param {Function} success  成功回调
 * @param {Function} fail  失败回调
 */
export function UploadFile({
	filePath = "",
	name = "",
	config = {},
	success = () => {},
	fail = () => {}
}) {
	return uni.uploadFile({
		url: `${requestConfig.baseUrl}${api.apiAttachmentupAttachmentupasync}`,
		filePath,
		name,
		header: {
			token: GetUUID()
		},
		formData: {
			FTYPE_ID: config.FTYPE_ID,
			FBUCKET_CODE: config.FBUCKET_CODE,
			pcode: config.FPROGRAM_CODE,
			type: config.FTYPE_NAME
		},
		success,
		fail
	});
};

/**
 * 根据Uid取权限
 * @hideLoading 
 */
export function GetMenuRightsByOpt({
	uid = ""
}) {
	let result = {
		view: false, //可用权限
		visible: false, //可见权限
		commonRight: {}, //通用权限
		specRight: {}, //特殊权限
	}
	if (!uid) {
		return result;
	}
	let menuRights = GetMenuRights();
	if (!Array.isArray(menuRights) || menuRights.length <= 0) {
		return result;
	}
	let items = menuRights.filter(p => p.FPROG_CODE == uid);
	if (!Array.isArray(items) || items.length <= 0) {
		return result;
	}

	for (let i = 0; i < items.length; i++) {
		if (!result.view) {
			result.view = items[i].FIF_VIEW;
		}
		if (!result.visible) {
			result.visible = items[i].FIF_VISIBLE;
		}

		if (items[i].FCOMMON_RIGHT) {
			var cr = this.DeSerialize(items[i].FCOMMON_RIGHT);
			Object.keys(cr).forEach(ri => {
				if (result.commonRight.hasOwnProperty(ri)) {
					if (result.commonRight[ri] < cr[ri]) {
						result.commonRight[ri] = cr[ri]
					}
				} else {
					result.commonRight[ri] = cr[ri];
				}
			});
		}

		if (items[i].FSPEC_RIGHT) {
			var sr = this.DeSerialize(items[i].FSPEC_RIGHT);
			Object.keys(sr).forEach(ri => {
				if (result.specRight.hasOwnProperty(ri)) {
					if (result.specRight[ri] < sr[ri]) {
						result.specRight[ri] = sr[ri]
					}
				} else {
					result.specRight[ri] = sr[ri];
				}
			});
		}
	}
	return result;
};

/*
下载图片文件
*/
export function DownLoadImageFile({
	api = "",
	successDownLoad = () => {},
	successSave = () => {},
	fail = () => {}
}) {
	// #ifdef MP-WEIXIN || H5
	uni.previewImage({
		urls: [api],
		success: successDownLoad,
		fail: fail
	});
	// #endif

	// #ifdef APP-PLUS
	uni.downloadFile({
		url: api,
		success: (res) => {
			if (res.statusCode === 200) {
				uni.saveImageToPhotosAlbum({
					filePath: res.tempFilePath,
					success: successSave,
					fail: fail,
				})
			}
		},
		fail: fail,
	});
	// #endif
};
/*
在H5复制内容到剪贴板的方法
*/
function CopyAtH5({
	text = ""
}) {
	const textarea = document.createElement("textarea");
	textarea.style.position = 'fixed';
	textarea.style.top = 0;
	textarea.style.left = 0;
	textarea.style.border = 'none';
	textarea.style.outline = 'none';
	textarea.style.resize = 'none';
	textarea.style.background = 'transparent';
	textarea.style.color = 'transparent';
	textarea.value = text; // 修改文本框的内容
	document.body.appendChild(textarea);
	textarea.select() // 选中文本
	try {
		const rtn = document.execCommand('copy');
		if (rtn) {
			ShowToast({
				title: "复制成功",
				icon: "success"
			});
		} else {
			ShowToast({
				title: "复制失败",
				icon: "none"
			});
		}
	} catch (err) {
		ShowToast({
			title: "复制失败",
			icon: "none"
		});
	}
	document.body.removeChild(textarea);
};

/*
复制内容到剪贴板
*/
export function SetClipboardData({
	text = ""
}) {
	// #ifdef H5
	CopyAtH5({
		text: text
	});
	// #endif

	// #ifdef MP-WEIXIN || APP-PLUS
	uni.setClipboardData({
		data: text,
		success: () => {
			ShowToast({
				title: "复制成功",
				icon: "success"
			});
		},
		fail: () => {
			ShowToast({
				title: "复制失败",
				icon: "none"
			});
		},
	});
	// #endif
};
/**
 *
 * 产生新排序号值(10,20,30)。
 * dataArray需要计算排序号的来源数据。需固定有排序号字段FDCSHOW_SEQNO
 *
 */
export function GenNewShowSeqNo(ops) {
	if (IsEmpty(ops)) {
		return;
	}

	var seqFldName = "FDCSHOW_SEQNO";
	var dataArray;
	if (IsArray(ops)) {
		dataArray = ops;
	} else if (IsObject(ops)) {
		if (ops.dataArray) {
			dataArray = ops.dataArray;
		}
		if (ops.seqFldName && !IsEmpty(ops.seqFldName)) {
			seqFldName = ops.seqFldName;
		}
	}
	if (IsEmpty(dataArray)) {
		return;
	}

	var list = new Array();
	dataArray.forEach(item => {
		list.push(item[seqFldName]);
	})
	// $.each(dataArray, function (a, item) {
	//     list.push(item[seqFldName]);
	// });

	var seqno = 10;
	if (list.length > 0) {
		seqno = Math.max.apply(Math, list);
		seqno = seqno - (seqno % 10) + 10;
	}

	return seqno;
};
