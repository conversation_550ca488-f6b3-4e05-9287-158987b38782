<template>
	<view class="item">
		<view class="item-content">
			<!-- 删除按钮 -->
			<!-- <view v-if="!item.FTAG_GEN_LOG_ID" class="delete-section">
				<hc-button 
					type="primary" 
					shape="square" 
					size="mini"
					class="delete-tag-btn" 
					@click="$emit('remove-tag', item, index)"
				>
					删除标签
				</hc-button>
			</view> -->
			
			<!-- 物料基本信息 -->
			<material-info-display 
				:item="item" 
				:edit-data="editData" 
				:show-total="true"
			/>
			
			<!-- 数量和仓库信息 -->
			<hc-row class="quantity-store-row">
				<hc-col :span="6">
					<view class="quantity-input-section">
						<text class="label">{{ editData.title }}:</text>
						<hc-input 
							v-if="!item.FTAG_GEN_LOG_ID"
							type="number" 
							v-model="item.FSTK_QTY"
							:customStyle="customStyle"
							class="quantity-input"
						/>
						<text v-else class="quantity-text">{{ item.FSTK_QTY }}</text>
					</view>
				</hc-col>
				<hc-col :span="6">
					<view class="store-section">
						<text class="label">仓库：</text>
						<text 
							:class="['store-text', { 'clickable': !item.FTAG_GEN_LOG_ID }]"
							:style="storeTextStyle"
							@click="!item.FTAG_GEN_LOG_ID && $emit('store-click', item, index)"
						>
							{{ formatStoreName(item) }}
						</text>
					</view>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
import MaterialInfoDisplay from './material-info-display.vue'

/**
 * 标签项目卡片组件
 * 用于显示标签登记中的单个标签项目
 */
export default {
	name: "tag-item-card",
	
	components: {
		MaterialInfoDisplay
	},
	
	props: {
		item: {
			type: Object,
			required: true
		},
		index: {
			type: Number,
			required: true
		},
		editData: {
			type: Object,
			required: true
		},
		customStyle: {
			type: Object,
			default: () => ({})
		}
	},
	
	computed: {
		storeTextStyle() {
			return {
				color: this.$hc?.requestConfig?.dataColor?.warn || '#007aff',
				'font-weight': 'bold'
			};
		}
	},
	
	methods: {
		/**
		 * 格式化仓库名称显示
		 */
		formatStoreName(item) {
			if (!item.FSTORE_NAME) {
				return '';
			}
			
			if (item.FSTORE_PLACE_NAME) {
				return `${item.FSTORE_NAME}/${item.FSTORE_PLACE_NAME}`;
			}
			
			return item.FSTORE_NAME;
		}
	}
}
</script>

<style lang="scss" scoped>
.item {
	width: 100%;
	display: flex;
	padding: 15rpx 12rpx;
	border-bottom: 2rpx solid #d1d1d1;
	background-color: #ffffff;
	
	&-content {
		flex: 1;
	}
}

.delete-section {
	text-align: right;
	margin-bottom: 10rpx;
	
	.delete-tag-btn {
		margin-left: 20rpx;
	}
}

.quantity-store-row {
	margin-top: 10rpx;
}

.quantity-input-section {
	display: flex;
	align-items: center;
	
	.label {
		font-size: 26rpx;
		color: #333;
		margin-right: 10rpx;
	}
	
	.quantity-input {
		flex: 1;
	}
	
	.quantity-text {
		font-size: 26rpx;
		color: #333;
	}
}

.store-section {
	display: flex;
	align-items: center;
	
	.label {
		font-size: 26rpx;
		color: #333;
		margin-right: 10rpx;
	}
	
	.store-text {
		font-size: 26rpx;
		
		&.clickable {
			cursor: pointer;
		}
	}
}
</style>
