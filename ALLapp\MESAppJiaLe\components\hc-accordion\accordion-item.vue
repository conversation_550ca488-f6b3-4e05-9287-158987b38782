﻿<template>
	<view class="hc-accordion-item" @tap.stop="tapHandle">
		<view class="hc-accordion-item-header" :style="{height: itemHeight + 'rpx', color: selected ? $themeCurrent.main : ''}">
			<view class="hc-accordion-item-header-text">
				<text style="margin-right: 10rpx;">{{record[titleKey]}}</text>
				<hc-icon v-if="selected" size="32" name="checkmark-circle" :color="$themeCurrent.main"></hc-icon>
			</view>
			<view style="display: inline-block;" @tap.stop="toggle">
				<hc-icon v-if="haveChildren" :name="collapse ? 'arrow-up' : 'arrow-down'"></hc-icon>
			</view>
		</view>
		<view class="hc-accordion-item-body" v-if="haveChildren && collapse">
			<accordion-item v-for="item in record[childrenKey]" :key="item[nodeKey]" :record="item"></accordion-item>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'accordionItem',
		componentName: 'accordionItem',
		inject: ["accordion"],
		props: {
			record: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				collapse: false,
			};
		},
		methods: {
			toggle() {
				this.collapse = !this.collapse;
			},
			tapHandle() {
				this.accordion.clickHandle(this.record);
			}
		},
		computed: {
			haveChildren() {
				const children = this.record[this.childrenKey];
				return children && children.length;
			},
			childrenKey() {
				return this.accordion.childrenKey;
			},
			titleKey() {
				return this.accordion.titleKey;
			},
			nodeKey() {
				return this.accordion.nodeKey;
			},
			selected() {
				const selectedKeys = this.accordion.selected || [];
				return selectedKeys.some(key => this.record[this.nodeKey] === key);
			},
			itemHeight() {
				return this.accordion.itemHeight;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-accordion-item {
		&-header {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: $hc-content-color;
			padding: 0 20rpx;
			border-bottom: 1px solid $uni-border-color;

			&-text {
				flex: 1;
				min-width: 0;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				padding-right: 10rpx;
			}
		}

		&-body {
			padding-left: 40rpx;
		}
	}

	.hc-accordion-item_active {
		background-color: $hc-bg-color;
	}
</style>
