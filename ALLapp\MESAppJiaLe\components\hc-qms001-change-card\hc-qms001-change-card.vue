<template>
	<view class="outBox">
		<view class="inputBox">
			<hc-form-item label="不良编号:" label-width="auto" prop="FCHECK_PERSON_NAME">
				<hc-input v-model="item.FBAD_REASON_CODE" disabled input-align="right"
					placeholder=" " :clearable="false">
				</hc-input>
			</hc-form-item>
			<hc-form-item label="不良原因:" label-width="auto" prop="FCHECK_PERSON_NAME">
				<hc-input v-model="item.FBAD_REASON_CODE" disabled input-align="right"
					placeholder=" " :clearable="false">
				</hc-input>
			</hc-form-item>
			
			<hc-form-item label="不良数量:" label-width="auto" prop="FCHECK_PERSON_NAME">
				<hc-input v-model="item.FNG_QTY" disabled input-align="right"
					placeholder=" " :clearable="false">
				</hc-input>
			</hc-form-item>
			<hc-form-item label="备注:" label-width="auto" prop="FCHECK_PERSON_NAME">
				<hc-input v-model="item.F_CUSTOM_FIELD1" disabled input-align="right"
					placeholder=" " :clearable="false">
				</hc-input>
			</hc-form-item>
			
			<view class="" style="height: 100rpx;display: flex;margin: 10rpx 0;">
				<hc-image v-for="i of images" :src="i.url" preview mode="aspectFit"
					style="width: 200rpx;"></hc-image>
			</view>
			
			
			<!-- <hc-row>
				<hc-col :span="2.8">
					<view class="item-content-type pro-left title-color">不良编号:</view>
				</hc-col>
				<hc-col :span="9.2">
					<hc-input style="height:55rpx;" v-model="item.FBAD_REASON_CODE" input-align="left" placeholder=""
						maxlength="50" :clearable="false" :disabled="true" :customStyle="customStyle"></hc-input>
				</hc-col>
			</hc-row> -->
			<!-- <hc-row>
				<hc-col :span="2.8">
					<view class="item-content-type pro-left title-color">不良原因:</view>
				</hc-col>
				<hc-col :span="9.2">
					<view class="text-val">
						{{item.FBAD_REASON_NAME}}
					</view>
				</hc-col>
			</hc-row> -->

			<!--<hc-row>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5">
							<view class="item-content-type pro-left title-color">不良张数:</view>
						</hc-col>
						<hc-col :span="8">
							<hc-input style="height:55rpx;" type="number" v-model="item.FNG_PCS" input-align="left"
								placeholder="" maxlength="3" :clearable="false" :disabled="true"
								:customStyle="customStyle"></hc-input>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="5.5">
							<view class="item-content-type pro-left title-color">抽检数量:</view>
						</hc-col>
						<hc-col :span="6.5">
							<hc-input style="height:55rpx;" type="number" v-model="item.FCHK_STIR_QTY"
								input-align="left" placeholder="" maxlength="500" :clearable="false"
								:disabled="true"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col> -->
				
				
				<!-- <hc-col :span="6">
					<hc-row>
						<hc-col :span="5.5">
							<view class="item-content-type pro-left title-color">不良数量:</view>
						</hc-col>
						<hc-col :span="6.5">
							<hc-input style="height:55rpx;" type="number" v-model="item.FNG_QTY" input-align="left"
								placeholder="" maxlength="500" :clearable="false" :disabled="true"
								:customStyle="customStyle"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>


			</hc-row>

			<hc-row v-if="item.F_CUSTOM_FIELD1">
				<hc-col :span="2.8">
					<view class="item-content-type pro-left title-color">备注:</view>
				</hc-col>
				<hc-col :span="9.2">
					<view class="text-val">
						{{item.F_CUSTOM_FIELD1}}
					</view>
				</hc-col>
			</hc-row>


			<hc-row v-if="images.length >0">
				<hc-col :span="12">
					<view class="" style="height: 100rpx;display: flex;margin: 10rpx 0;">
						<hc-image v-for="i of images" :src="i.url" preview mode="aspectFit"
							style="width: 200rpx;"></hc-image>
					</view>
				</hc-col>
			</hc-row>


			<!--<hc-row>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5.5">
							<view class="item-content-type pro-left title-color">总用量:</view>
						</hc-col>
						<hc-col :span="6.5">
							<hc-input style="height:55rpx;" type="number" v-model="item.FUSE_QTY" input-align="left"
								placeholder="" maxlength="500" :clearable="false" :customStyle="customStyle"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5">
							<view class="item-content-type pro-left title-color">单位:</view>
						</hc-col>
						<hc-col :span="8">
							<hc-input style="height:55rpx;" type="text" v-model="item.FSUB_UNIT_NAME" input-align="left"
								placeholder="" maxlength="500" :disabled="true" :clearable="false"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="5.5">
							<view class="item-content-type pro-left title-color">是否倒扣料:</view>
						</hc-col>
						<hc-col :span="6.5">
							<hc-checkbox style="height:55rpx;" type="text" v-model="item.FIF_REVERSE" input-align="left"
								placeholder=""></hc-checkbox>
						</hc-col>
					</hc-row>
				</hc-col>

			</hc-row> -->
		</view>
	</view>
</template>

<script>
	export default {
		name: "hc-workOrder-change-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
			mst: {
				type: Object,
				default () {
					return {}
				}
			},
			images: {
				type: Array,
				default () {
					return [
						// 	{
						// 	url: "./static/menuLogo/WorkFetchApp.png"
						// }, {
						// 	url: "./static/menuLogo/WorkFetchApp.png"
						// }, {
						// 	url: "./static/menuLogo/WorkFetchApp.png"
						// },
					]
				}
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				}
			};
		},
		computed: {
			FCHANGE_TYPE_NAME(e) {
				return this.item.FCHANGE_TYPE == 'update' ? "编辑" : this.item.FCHANGE_TYPE == 'add' ? "新增" : "删除"
			}
		},
		methods: {
			//更变类型
			updateType(item, index) {
				this.$emit("updateType", {
					item,
					index
				})
				// this.updateTypeList = this[item.FCHANGE_TYPE];
				// this.selectShow = true;
				// this.itemIndex = index;
			},


		}
	}
</script>

<style lang="scss" scoped>
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		padding-bottom: 0;

		// background-color: red;
		border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 10rpx;
			min-width: 160rpx;
			height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 10rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.title {
		font-size: 28rpx;
		line-height: 50rpx;
		font-weight: bold;
		margin: 5rpx 0;
		color: #000000 !important;
	}

	.desc {
		display: flex;
		color: $u-tips-color;
		margin: 10rpx 0;
	}

	.desc view {
		// width: 36%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.desc-title {}

	.pro-left {
		// width: 500rpx;
		font-size: 28rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		text-align: right;
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.inputBox {
		padding-left: 12rpx;
	}

	.outBox {
		border-top: 10rpx #EEF1F5 solid;
		border-bottom: 10rpx #EEF1F5 solid;
		border-left: 15rpx #EEF1F5 solid;
		border-right: 15rpx #EEF1F5 solid;
		// margin: 15rpx 12rpx;
		// display: flex;
	}

	.title-color {
		color: #666666;
	}

	.text-val {
		font-size: 26rpx;
		// font-weight: bold;
		line-height: 34rpx;
		word-break: break-all; // word不能折行连续的感叹号 ！！！
		word-wrap: break-word;
		overflow-wrap: break-word; // word-wrap别名，css3属性，都写上，万一以后word-wrap去掉了呢

	}
</style>