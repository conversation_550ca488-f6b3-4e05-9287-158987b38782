<template>
	<view class="card-out-box" v-if="item" :key="index" @tap="Tap">
		<!-- <hc-read-more :toggle="read" style="width: 100%;text-indent: 0;" show-height="300" > -->
		<view>
			<hc-row>
				<hc-col :span="12">
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label class="big-title">
								<text>{{model.FSALE_ORDER_NO}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label class="big-title">
								<text>{{item.FSALE_ORDER_NO}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FSTATUS_NAME}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FSTATUS_NAME}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FCUST_NAME}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FCUST_NAME}}</text>
							</label>
						</hc-col>
					</hc-row>
					<hc-row>
						<hc-col :span="4" class="item-content-title pro-desc">
							<label>
								<text>{{model.FBILL_DATE}}：</text>
							</label>
						</hc-col>
						<hc-col :span="8">
							<label>
								<text>{{item.FBILL_DATE.split(' ')[0]}}</text>
							</label>
						</hc-col>
					</hc-row>
				</hc-col>
			</hc-row>
		</view>
		<!-- </hc-read-more> -->
	</view>
</template>

<script>
	export default {
		name: "hc-copOrder-card",
		props: {
			read: {
				type: Boolean,
				default: true
			},
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				model: {
					FSALE_ORDER_NO: "销售订单编号",
					FSTATUS_NAME: "审核状态",
					FCUST_NAME: "客户名称",
					FBILL_DATE: "单据日期",
				}
			};
		},
		methods: {
			Tap() {
				this.$emit("click", this.item)
			},
		}
	}
</script>
<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;

		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				text-align: right;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}

		.item-content {

			text-indent: 0;
		}
	}

	.Focus {
		color: red;
		font-weight: 900;
	}

	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.card-out-box {
		margin: 10rpx;
		border-radius: 6rpx;
		background-color: #FFFFFF;
		padding: 15rpx 0;
	}

	.big-title {
		font-size: 30rpx;
		font-weight: bold;
	}
</style>
