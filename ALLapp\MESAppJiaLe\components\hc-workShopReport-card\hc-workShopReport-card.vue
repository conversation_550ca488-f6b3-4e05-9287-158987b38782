<template>
	<view class="card-out-box" v-if="item" :key="index">
		<!-- <hc-read-more :toggle="read" style="width: 100%;text-indent: 0;" show-height="300" > -->
		<view>
			<hc-row>
				<hc-col :span="6" class="item-content-title pro-desc">
					<label style="font-weight: bold;">
						<text>生产车间：{{item.FMAKER_NAME}}</text>
					</label>
				</hc-col>
				<hc-col :span="6" class="item-content-title pro-desc">
					<label>
						<text>产品类别：{{item.FMATERIAL_CATE_NAME}}</text>
					</label>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="6" class="item-content-title pro-desc">
					<label>
						<text>当期订单：{{item.sumOrdQty||0}}</text>
					</label>
				</hc-col>
				<hc-col :span="6" class="item-content-title pro-desc">
					<label>
						<text>当期完工：{{item.sumInQty||0}}</text>
					</label>
				</hc-col>
			</hc-row>
			<hc-row>
				<hc-col :span="6" class="item-content-title pro-desc">
					<label>
						<text>未完工数：{{item.sumOrdQty-item.sumInQty||0}}</text>
					</label>
				</hc-col>
				<hc-col :span="6" class="item-content-title pro-desc">
					<label>
						<text>当期发货：{{item.sumOutQty||0}}</text>
					</label>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	export default {
		name: "hc-workShopReport-card",
		props: {
			read: {
				type: Boolean,
				default: true
			},
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {

			};
		},
		methods: {}
	}
</script>
<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;

		}

		&-content {
			&-title {
				font-size: 30rpx;
				line-height: 50rpx;
				text-align: left;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}

		.item-content {

			text-indent: 0;
		}
	}

	.Focus {
		color: red;
		font-weight: 900;
	}

	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.card-out-box {
		margin: 15rpx 20rpx 15rpx 20rpx;
		border-radius: 10rpx;
		background-color: #FFFFFF;
		padding: 15rpx 0rpx 15rpx 20rpx;
	}

	.big-title {
		font-size: 30rpx;
		font-weight: bold;
	}
</style>
