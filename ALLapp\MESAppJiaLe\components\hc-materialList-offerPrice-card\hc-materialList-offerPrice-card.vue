<template>
	<view class="">
		<view class="outBox item" v-if="item" :key="index">
			<view class="leftBox">
				<hc-image :src="$hc.Command.GetAttachmentURL(item.FPIC_ATTACH_ID)"></hc-image>
			</view>
			<view class="rightBox">
				<view class="title">
					<span>{{ item.FMATERIAL_NAME||"" }}</span>
					<span class="bottomResult success" v-if="item.ResultBool">获取底价成功</span>
					<span class="bottomResult fail" v-else>获取底价失败</span>
				</view>
				<view class="desc">
					<view style="width: 100%;"><span class="desc-title">编号:</span>{{ item.FMATERIAL_CODE||"" }}</view>
				</view>
				<view class="desc">
					<view>规格:{{ item.FSPEC_DESC||"" }}</view>
				</view>
			</view>
		</view>

		<view class="bottomBox">
			<hc-row>
				<hc-col :span="6">
					<!-- <view class="item-content-type pro-left">状态:{{ item.FUSE_QTY||0 }}</view> -->
					<hc-row>
						<hc-col :span="6">
							<view class="item-content-type pro-left">单个加价:</view>
						</hc-col>
						<hc-col :span="6">
							<hc-input type="number" v-model="item.FADD_UP" :customStyle="customStyle"
								@blur="FADD_UPChange" :clearable="false"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="6">
							<view class="item-content-type pro-left">数量:</view>
						</hc-col>
						<hc-col :span="6">
							<hc-input type="number" v-model="item.FEVAL_QTY" :customStyle="customStyle"
								@blur="FEVAL_QTYChange" :clearable="false"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
			</hc-row>
		</view>

		<view class="bottomBox">
			<hc-row>
				<hc-col :span="6">
					<hc-row>
						<hc-col :span="6">
							<view class="item-content-type pro-left">底价:</view>
						</hc-col>
						<hc-col :span="6">
							<hc-input type="number" v-model="item.FEVAL_UP" :customStyle="customStyle"
								@blur="FEVAL_UPChange" :clearable="false"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
				<hc-col :span="6">
					<!-- <view class="item-content-type pro-left">总价:{{ item.FUSE_QTY||0 }}</view> -->
					<hc-row>
						<hc-col :span="6">
							<view class="item-content-type pro-left">总价:</view>
						</hc-col>
						<hc-col :span="6">
							<hc-input type="number" v-model="item.FEVAL_AMT" :customStyle="customStyle" disabled
								placeholder=""></hc-input>
						</hc-col>
					</hc-row>
				</hc-col>
			</hc-row>
		</view>

	</view>
</template>

<script>
	/*
	 * @example <hc-material-recv-card item="{}" index="0"></hc-material-recv-card>
	 */
	export default {
		name: "hc-materialList-offerPrice-card",
		props: {
			//父组件传递的数据
			item: {
				type: Object,
				default () {
					return {}
				}
			},

			//父组件传递的下标值
			index: {
				type: Number,
				default: 0
			},

			//父组件传递的子组件改变后更新父组件的方法
			waitChildNum_UPChange: {
				type: Function,
				default: null,
			},

		},
		data() {
			return {
				FADD_UP_BAK: 0, //备份的加价变量，每次加价改变判断大小，是增是减

				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				}
			};
		},

		watch: {

		},

		methods: {

			//单个加价改变时逻辑
			FADD_UPChange(e) {

				e = Number(e)
				let FADD_UP = e

				if (!e) { //如果是空转为0
					e = 0
					FADD_UP = 0
				}
				if (e == this.FADD_UP_BAK) { //如果和备份（变前值）的一样，则不改变
					return false
				}
				let difference = e - Number(this.FADD_UP_BAK) //算出差距
				this.FADD_UP_BAK = e //将当前值备份，以备后面判断增减

				let FEVAL_UP = Number(this.item.FEVAL_UP) + difference //底价=底价加上差距
				let FEVAL_AMT = Number(FEVAL_UP) * Number(this.item.FEVAL_QTY) //总价=底价*数量
				let FEVAL_QTY = this.item.FEVAL_QTY //数量不用改变

				let data = {
					FMATERIAL_ID: this.item.FMATERIAL_ID,
					FMATERIAL_NAME: this.item.FMATERIAL_NAME,
					changeData: {
						FADD_UP,
						FEVAL_UP,
						FEVAL_AMT,
						FEVAL_QTY
					},
				}
				this.waitChildNum_UPChange(data) //触发父组件的方法，并传递data
			},

			//底价改变时逻辑
			FEVAL_UPChange(e) {
				e = Number(e)
				let FEVAL_UP = e

				if (!e) { //如果是空转为0
					e = 0
					FEVAL_UP = 0
				}
				let FEVAL_AMT = Number(FEVAL_UP) * Number(this.item.FEVAL_QTY) //总价=底价*数量
				let FEVAL_QTY = this.item.FEVAL_QTY //数量不用改变
				let FADD_UP = this.item.FADD_UP //单个加价不变

				let data = {
					FMATERIAL_ID: this.item.FMATERIAL_ID,
					FMATERIAL_NAME: this.item.FMATERIAL_NAME,
					changeData: {
						FADD_UP,
						FEVAL_UP,
						FEVAL_AMT,
						FEVAL_QTY
					},
				}
				this.waitChildNum_UPChange(data) //触发父组件的方法，并传递data
			},

			//数量改变时逻辑
			FEVAL_QTYChange(e) {
				e = Number(e)
				let FEVAL_QTY = e

				if (!e) { //如果是空转为0
					e = 0
					FEVAL_QTY = 0
				}
				let FEVAL_AMT = Number(this.item.FEVAL_UP) * Number(FEVAL_QTY) //总价=底价*数量
				let FEVAL_UP = this.item.FEVAL_UP //底价不用改变
				let FADD_UP = this.item.FADD_UP //单个加价不变

				let data = {
					FMATERIAL_ID: this.item.FMATERIAL_ID,
					FMATERIAL_NAME: this.item.FMATERIAL_NAME,
					changeData: {
						FADD_UP,
						FEVAL_UP,
						FEVAL_AMT,
						FEVAL_QTY
					},
				}
				this.waitChildNum_UPChange(data) //触发父组件的方法，并传递data
			},
		}
	}
</script>

<style lang="scss">
	.item {
		// width: 100%;
		display: flex;
		padding: 15rpx 12rpx;

		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 20rpx;
			// min-width: 160rpx;
			// max-width: 160rpx;
			// height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-left {
		// width: 500rpx;
		//width: calc(100%/12);
		// width: 100%;
		// overflow: hidden;
		// text-overflow: ellipsis;
		white-space: nowrap
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.outBox {
		padding: 15rpx 12rpx 0 12rpx;
		display: flex;
	}

	.leftBox {
		width: 25%;
		height: 160rpx;
		min-width: 180rpx;
	}

	.rightBox {
		margin-left: 15rpx;
		width: 75%;
	}

	.title {
		display: flex;
		font-size: 28rpx;
		line-height: 50rpx;
		font-weight: bold;
		margin: 5rpx 0;
		color: #000000 !important;
	}

	.bottomResult {
		margin-left: auto;
		font-size: 18rpx;
	}

	.success {
		color: green;
	}

	.fail {
		color: red;
	}

	.desc {
		display: flex;
		color: $u-tips-color;
		margin: 10rpx 0;
	}

	.desc view {
		// width: 36%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.bottomBox view {
		// display: flex;
		padding: 0 12rpx;
	}

	.pro-left {
		// width: 500rpx;
		font-size: 28rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.desc-title {}
</style>
