import {
	mapGetters
} from 'vuex'
import {
	gettersKeys
} from '../store/index.js'

module.exports = {
	data() {
		return {}
	},
	props: {
		// 权限控制key
		authKey: String,
	},
	onShow() {
		// 页面切换 摧毁audio实例
		this.$hc.audioClear();
		const {
			UId
		} = this.$options;
		if (UId) {
			// 覆盖原型上的$hc.On
			const originOn = this.$hc.On;
			this.$hc.On = function(uid, name, callback) {
				// 参数调整
				if (typeof name === "function") {
					callback = name;
					name = uid;
					uid = UId;
				}
				originOn(uid, name, callback)
			}
			// 覆盖原型上的$hc.Off
			const originOff = this.$hc.Off;
			this.$hc.Off = function(uid, name, callback) {
				// 参数调整
				if (typeof name === "function") {
					callback = name;
					name = uid;
					uid = UId;
				}
				originOff(uid, name, callback)
			}
			// 修改globalData中的UId
			if (getApp()) getApp().globalData.UId = UId;

			// 根据UId 不控制权限的页面
			if (!this.$hc.requestConfig.excludeRootUId[UId]) {
				// 根据UId获取权限
				const root = this.$hc.Command.GetMenuRightsByOpt({
					uid: UId
				}) || {};
				const {
					view,
					visible,
					commonRight = {},
					specRight = {}
				} = root;

				if (view && visible) {
					// 权限合并
					const refRoot = { ...commonRight,
						...specRight
					};
					// 更新
					this.$store.dispatch("setRefRoot", refRoot);
				} else {
					// 根据权限信息 无权限 重定向到无权限页面
					this.$hc.Command.RedirectTo("NoPermissionAppPage")
				}
			}
		} else {
			// 清空UId 防止无UId页面 延用上次的UId
			if (getApp()) getApp().globalData.UId = "";
		}
	},
	onLoad() {
		// getRect挂载到$hc上，因为这方法需要使用in(this)，所以无法把它独立成一个单独的文件导出
		this.$hc.getRect = this.$hcGetRect
	},
	methods: {
		/**
		 * 字符串数组打散成字符串
		 * @param {Array} arr
		 * @param {String} separator 分隔符 默认 "\\"
		 * @returns {String}
		 */
		$arrJoinBySeparator(arr, separator) {
			if (!separator && separator !== "") {
				separator = "\\";
			}
			return arr.join(separator);
		},
		// 更新页面权限 by UId
		$updateRootByUId(UId) {
			// 根据UId获取权限
			const root = this.$hc.Command.GetMenuRightsByOpt({
				uid: UId
			}) || {};
			// 更新权限
			this.$store.dispatch("setRefRoot", refRoot);
		},
		// 查询节点信息
		$hcGetRect(selector, all) {
			return new Promise(resolve => {
				uni.createSelectorQuery().
				in(this)[all ? 'selectAll' : 'select'](selector)
					.boundingClientRect(rect => {
						if (all && Array.isArray(rect) && rect.length) {
							resolve(rect)
						}
						if (!all && rect) {
							resolve(rect)
						}
					})
					.exec()
			})
		},
		// 通过authKey获取权限
		$getAuthByAuthKey(authKey) {

			// 0 无权限
			if(authKey)
			{				
				const refRoot = this.$store.state.refRoot;
				return refRoot[authKey] === 0 ? false : true;
			}
			else
			{
				return true;
			}
		}
	},
	computed: {
		$haveAuth() {
			return this.$getAuthByAuthKey(this.authKey);
		},
		// 显式申明在根getters的state 映射并打散合并到computed属性（.vue文件内可以直接通过this[key]访问）
		...mapGetters(gettersKeys)
	},
	onReachBottom() {
		uni.$emit('uOnReachBottom')
	}
}
