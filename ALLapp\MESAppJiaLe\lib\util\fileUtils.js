﻿//参考：http://www.html5plus.org/doc/zh_cn/io.html#plus.io.PUBLIC_DOCUMENTS
//读取本地本地文件（安卓sd卡或ios沙盒）
const fileRead = ({
	filename //相对路径或者绝对路径
	,
	fullPath //可指定目录
}) => new Promise(
	(resolve, reject) => {
		var resInfo = {
			Info: '',
			size: 0,
			name: ''
		};

		//#ifdef APP-PLUS
		/*
		1、请求本地文件系统对象 plus.io.requestFileSystem
			程序公用文档目录常量：
				plus.io.PUBLIC_DOCUMENTS，
				路径：/sdcard/Android/data/%PACKAGENAME%/documents
		2、fobject.root.getFile 获取 文件系统中的文件对象 fileEntry
		3、fileEntry.file 获取文件数据对象
		4、fileReader.readAsText 以文本格式读取文件数据内容
		5、fileReader.onloadend 文件读取操作完成时的回调函数 
		*/
		plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS, function(fobject) {
			if (fullPath) {
				fobject.root.fullPath = fullPath
			}
			fobject.root.getFile(filename, {
				create: false
			}, function(fileEntry) {
				fileEntry.file(function(file) {
					var fileReader = new plus.io.FileReader();
					fileReader.readAsText(file, 'utf-8');
					fileReader.onloadend = function(evt) {
							resInfo = {
								Info: evt.target.result,
								size: file.size,
								name: file.name
							}
							resolve(resInfo)
						},
						function(e) {
							reject(e)
						}
				}, function(e) {
					reject(e)
				});
			}, function(e) {
				reject(e)
			});
		}, function(e) {
			reject(e)
		});
		// #endif
		// #ifndef APP-PLUS
		reject(new Error("仅APP端支持文件读取"))
		// #endif
	}
)

//将json内容写入本地（安卓sd卡，ios沙盒）
// #ifdef APP-PLUS 
const fileWriter = function(filename, jsonData, success, fail) {
	plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS,
		function(fs) {
			//fs.root 是根目录操作对象
			//创建文件夹
			// fs.root.getDirectory("uniapp_data", {//创建一个文件夹名为uniapp_data
			// 	create: true,
			// 	exclusive: false
			// }, function(dir) {
			//  dir.getFile
			fs.root.getFile(filename, { //在uniapp_data文件夹中创建一个json文件
					create: true
				},
				function(fileEntry) {
					fileEntry.file(function(file) {
						//写入文件
						fileEntry.createWriter(
							function(writer) {
								// writer.onwritestart = function(e) {
								// 	console.log("写入数据开始");
								// }
								writer.onwrite = function(e) {
									// console.log("写入数据成功");
									success(e);
								}
								//定位至文件结尾，即每次都是追加内容
								//writer.seek(writer.length);
								//定位至开头，即每次都是重写文件。（默认）
								//writer.seek(0);
								writer.write(jsonData);
							},
							function(error) {
								// fail()
								console.log("创建Writer失败" + error.message);
							});

						////读取文件
						// var fileReader = new plus.io.FileReader();
						// console.log("getFile:" + JSON.stringify(file));
						// fileReader.readAsText(file, 'utf-8');
						// fileReader.onloadend = function(evt) {
						// 	console.log("11" + JSON.stringify(evt));
						// 	console.log("evt.target" + JSON.stringify(evt.target));
						// 	console.log("evt.target.result" + JSON.stringify(evt.target.result));
						// }
					});
				},
				function(err) {
					// fail()
					console.log("访问File失败" + err.message);
				})
		},
		function(error) {
			// fail()
			console.log("访问_DOC目录失败" + error.message);
		});
}
//#endif

//删除文件（安卓sd卡，ios沙盒）
// #ifdef APP-PLUS 
const fileDelete = function(filename, success, fail) {
	plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS,
		function(fs) {
			//fs.root 是根目录操作对象
			//创建文件夹
			fs.root.getFile(filename, { //在uniapp_data文件夹中创建一个json文件
					create: false
				},
				function(fileEntry) {
					fileEntry.remove( success, fail);
				},
			)
		},
		function(error) {
			// fail()
			console.log("访问_DOC目录失败" + error.message);
		});
}
//#endif

export default {
	getfileInfo,
	fileWriter,
	fileDelete
}
