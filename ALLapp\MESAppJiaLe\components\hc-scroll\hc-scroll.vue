<template>
	<hc-mescroll-uni :fixed="false" ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback"
		:up="upOptions" :down="downOptions">
		<slot></slot>
	</hc-mescroll-uni>
</template>

<script>
	import MescrollMixin from "../hc-mescroll-uni/mescroll-mixins.js";
	/**
	 * scroll
	 * @description 高度自适应 滚动区域 下拉刷新 加载更多
	 * @property {Boolean} enabledRefresher 开启下拉刷新功能
	 * @example <hc-scroll @refresh="" @loadmore=""></hc-scroll>
	 */
	export default {
		name: "hc-scroll",
		mixins: [MescrollMixin],
		data() {
			return {

			};
		},
		props: {
			// 开启下拉刷新功能
			enabledRefresher: {
				type: Boolean,
				default: true
			},
			// 上拉加载的配置参数
			upConfig: {
				type: Object,
				default () {
					return {}
				}
			},
			// 下拉刷新的配置参数
			downConfig: {
				type: Object,
				default () {
					return {}
				}
			},
			size: {
				type: Number,
				default: 10
			}
		},
		methods: {
			downCallback(ops) {
				ops.size = this.size;
				this.$emit("refresh", ops);
			},
			upCallback(ops) {
				ops.size = this.size;
				this.$emit("loadmore", ops);
			}
		},
		computed: {
			upOptions() {
				return {
					textNoMore: '-- 没有更多了 --',
					auto: this.enabledRefresher,
					use: this.enabledRefresher,
					isBounce: true,
					...this.upConfig
				}
			},
			downOptions() {
				return {
					use: this.enabledRefresher,
					auto: false,
					isBounce: true,
					...this.downConfig,
				}
			},
		}
	}
</script>

<style>
</style>
