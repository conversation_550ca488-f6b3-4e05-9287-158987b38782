<template>
	<view class="hc-icon-img" @tap="tapHandle">
		<!-- <view class="hc-icon-text-icon" :style="[iconStyle]">
			<hc-icon :icon="icon" :size="60"></hc-icon>
			<slot name="superscriptIcon"></slot>
		</view> -->

		<view class="hc-icon-img-icon">
			<image class="hc-icon-img-img" :src="url"></image>
			<!-- {{url}} -->
		</view>
		<view class="hc-icon-img-text">{{text}}</view>
	</view>
</template>

<script>
	/**
	 * hc-icon-text 图标文字
	 * @description 适用于上部分为图标下部分为文字
	 * @property {String} text 文字
	 * @property {String} icon 图标名
	 * @property {String} iconBgColor 图标背景
	 * @example <hc-icon-text text="标题" :icon="icon-setting"></hc-icon-text>
	 */
	export default {
		name: 'hc-icon-img',
		data() {
			return {
				// url: `@/static/menuLogo/${this.icon}.png`
			};
		},
		props: {
			icon: {
				type: String,
				default: ''
			},
			// iconBgColor: {
			// 	type: String,
			// 	default: ''
			// },
			text: {
				type: String,
				default: ''
			}
		},

		computed: {
			url() {
				let _this = this
				let url = require(`@/static/menuLogo/${_this.icon}.png`) //转地址需要加require转base64格式
				return url
			}
		},
		methods: {
			tapHandle() {
				this.$emit("click");
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-icon-img {
		text-align: center;

		&-icon {
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 30rpx auto 10rpx auto;
			width: 70rpx;
			height: 70rpx;
			// border-radius: 12rpx;
			
		}

		&-img {
			width: 100%;
			height: 100%;
		}

		&-text {
			font-size: 26rpx;
			font-weight: 700;
			// margin-top: 10rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			color: #6b6b6b;
		}
	}
</style>
