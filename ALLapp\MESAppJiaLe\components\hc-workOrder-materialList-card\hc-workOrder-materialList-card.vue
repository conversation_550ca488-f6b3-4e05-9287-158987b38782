<template>
	<view class="outBox item" v-if="item" :key="index">
		<view class="leftBox">
			<hc-image :src="$hc.Command.GetAttachmentURL(item.FPIC_ATTACH_ID)"></hc-image>
		</view>
		<view class="rightBox">
			<view class="title">
				{{ item.FMATERIAL_NAME||"" }}
			</view>
			<view class="desc">
				<view style="width: 100%;"><span class="desc-title">编号:</span>{{ item.FMATERIAL_CODE||"" }}</view>
				<!-- <view>规格:{{ item.FSPEC_DESC||"" }}</view> -->
			</view>
			<view class="desc">
				<view style="width: 28%;"><span class="desc-title">单位:</span>{{ item.FSUB_UNIT_NAME||"" }}</view>
				<view style="width: 40%;"><span class="desc-title">待投料量:</span>{{ item.FFETCH_WAIT_QTY||0 }}</view>
				<view style="width: 32%;"><span class="desc-title">现存量:</span>{{ item.StoreQty||0 }}</view>
			</view>
		</view>
	</view>

</template>

<script>
	/*
	 * @example <hc-material-recv-card item="{}" index="0"></hc-material-recv-card>
	 */
	export default {
		name: "hc-workOrder-materialList-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
			editData: {
				type: Object,
				default () {
					return {
						title: "",
						field: '',
						// fieldQty:'',
					}
				}
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					"font-weight": "bold"
				}
			};
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			},
		}
	}
</script>

<style lang="scss">
	.item {
		// width: 100%;
		display: flex;
		padding: 15rpx 12rpx;

		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 20rpx;
			// min-width: 160rpx;
			// max-width: 160rpx;
			// height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.pro-left {
		// width: 500rpx;
		//width: calc(100%/12);
		// width: 100%;
		// overflow: hidden;
		// text-overflow: ellipsis;
		white-space: nowrap
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.outBox {
		padding: 15rpx 12rpx;
		display: flex;
	}

	.leftBox {
		width: 25%;
		height: 160rpx;
		min-width: 180rpx;
	}

	.rightBox {
		margin-left: 15rpx;
		width: 75%;
	}

	.title {
		font-size: 28rpx;
		line-height: 50rpx;
		font-weight: bold;
		margin: 5rpx 0;
		color: #000000 !important;
	}

	.desc {
		display: flex;
		color: $u-tips-color;
		margin: 10rpx 0;
	}

	.desc view {
		// width: 36%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.desc-title{
		
	}
	
	
</style>
