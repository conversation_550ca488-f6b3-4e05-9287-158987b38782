﻿<template>
	<view class="hc-audio" @tap="toggleHandle">
		<image :src="imageSrcList[currentAudiIndex]" :style="[customStyle]"></image>
		<text class="hc-audio-duration">{{formatDuration}}</text>
	</view>
</template>

<script>
	import audio1 from './audio1.png';
	import audio2 from './audio2.png';
	import audio3 from './audio3.png';
	const audioInstanceCollection = getApp().globalData.audioInstanceCollection;
	export default {
		data() {
			return {
				imageSrcList: [
					audio1,
					audio2,
					audio3
				],
				currentAudiIndex: 2,
			};
		},
		props: {
			src: String,
			size: {
				type: Number,
				default: 36
			},
			duration: {
				type: Number,
				default: 0
			},
			extName: {
				type: String,
				default: ".mp3"
			}
		},
		watch: {
			src(newVal) {
				this.destroyInnerAudioContext();
			}
		},
		computed: {
			formatDuration() {
				const duration = this.duration;
				let s = Math.round(duration % 60);
				let m = Math.floor(duration / 60);
				return m ? `${m}'${s}''` : `${s}''`;
			},
			customStyle() {
				return {
					width: `${this.size}rpx`,
					height: `${this.size}rpx`
				}
			}
		},
		// 修复uni-app createInnerAudioContext 正常方式使用巨多bug  修复： 播放暂停直接采用摧毁实例的方式
		methods: {
			toggleAudioImageSrc() {
				this.currentAudiIndex++;
				if (this.currentAudiIndex > 2) {
					this.currentAudiIndex = 0
				}
			},
			enableAudioPlayTimer() {
				return setInterval(() => {
					this.toggleAudioImageSrc();
				}, 500)
			},
			toggleHandle() {
				if (!this.innerAudioContext) {
					this.contextInit();
					audioInstanceCollection.push(this.destroyInnerAudioContext);
					this.timer = this.enableAudioPlayTimer();
					this.toggleAudioImageSrc();
				} else {
					this.destroyInnerAudioContext();
				}
			},
			contextInit(src = this.src) {
				// 卸载其他audio
				audioInstanceCollection.forEach(fn => fn());
				const innerAudioContext = uni.createInnerAudioContext();
				innerAudioContext.src = `${src}${this.extName}`;
				innerAudioContext.autoplay = true;
				innerAudioContext.onEnded(() => {
					this.$emit("ended");
					this.destroyInnerAudioContext();
				});
				innerAudioContext.onError((res) => {
					this.$emit("error", res);
					this.destroyInnerAudioContext();

				});
				this.innerAudioContext = innerAudioContext;
			},
			destroyInnerAudioContext() {
				if (this.innerAudioContext) {
					this.innerAudioContext.destroy();
					this.innerAudioContext = null;
					if (this.timer) {
						clearInterval(this.timer);
						this.timer = null;
						this.currentAudiIndex = 2;
					}
				}
			}
		},
		beforeDestroy() {
			this.destroyInnerAudioContext();
		}
	}
</script>

<style lang="scss" scoped>
	.hc-audio {
		display: flex;
		align-items: center;
		border-radius: 6rpx;
		background-color: #F8F8F8;
		color: #000;
		padding: 10rpx;

		&-duration {
			margin-left: 10rpx;
		}
	}
</style>
