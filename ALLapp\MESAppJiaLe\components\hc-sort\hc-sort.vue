﻿<template>
	<view :class="['hc-sort', {'hc-sort_asc': mode === 'asc'}, {'hc-sort_des': mode === 'des'}]" @tap="tapHandle">
		<view class="hc-sort-up" :style="{borderColor:  mode === 'asc' ? `transparent transparent ${$themeCurrent.main}` : null}"></view>
		<view class="hc-sort-down" :style="{borderColor:  mode === 'des' ? `${$themeCurrent.main} transparent transparent` : null}"></view>
	</view>
</template>

<script>
	/**
	 * hc-sort 排序状态显示
	 * @description 排序状态显示
	 * @property {String} mode 模式 升序降序不排序
	 * @example <hc-sort mode=""></hc-sort>
	 */
	export default {
		data() {
			return {

			};
		},
		props: {
			mode: {
				type: String,
				default: ""
			},
		},
		methods: {
			tapHandle() {
				this.$emit("change", this.mode);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hc-sort {
		display: inline-block;

		&-up {
			width: 0;
			height: 0;
			border-width: 0 10rpx 10rpx;
			border-style: solid;
			border-color: transparent transparent $hc-tips-color;
		}

		&-down {
			border-width: 10rpx 10rpx 0;
			border-style: solid;
			border-color: $hc-tips-color transparent transparent;
			margin-top: 4rpx;
		}
	}
</style>
