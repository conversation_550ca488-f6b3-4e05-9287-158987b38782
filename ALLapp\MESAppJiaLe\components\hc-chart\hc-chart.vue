﻿<template>
	<view class="hc-chart" :style="{width: width, height: height}">
		<canvas v-if="cWidth" :id="canvasId" :canvasId="canvasId" :style="{'width':cWidth*pixelRatio+'px','height':cHeight*pixelRatio+'px', 'transform': 'scale('+(1/pixelRatio)+')','margin-left':cWidth*(pixelRatio-1)/2+'px','margin-top':cHeight*(pixelRatio-1)/2+'px'}"
		 @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd" @error="error">
		</canvas>
	</view>
</template>

<script>
	import uCharts from './u-charts.js';
	var canvases = {};
	// 图例默认配置
	const legendConfig = {
		show: true,
		position: 'top'
	};
	// x轴默认配置
	const xAxisConfig = {
		itemCount: 4,
		scrollShow: true
	};
	// 基础默认配置
	const baseConfig = {
		// 画布填充边距，顺序为上右下左，同css，但必须4位
		padding: [15, 15, 4, 15],
		// 横屏模式，默认为false
		rotate: false,
		// 锁定横屏模式，如果在支付宝和百度小程序中使用横屏模式，请赋值true，否则每次都会旋转90度。跨端使用通过uni-app的条件编译来赋值
		rotateLock: false,
		// 全局默认字体大小（可选，单位为px，默认13px）高分屏不必乘像素比，自动根据pixelRatio计算
		fontSize: 13,
		// 	canvas背景颜色（如果页面背景颜色不是白色请设置为页面的背景颜色，默认#ffffff）
		background: '#FFFFFF',
		// 是否开启图表可拖拽滚动 默认false 支持line, area, column, candle图表类型(需配合绑定@touchstart, @touchmove, @touchend方法)
		enableScroll: true,
		// 是否显示辅助线 默认false 支持line, area, column, candle, mix图表类型
		enableMarkLine: false,
		// 是否动画展示
		animation: true,
		// 动画展示时长单位毫秒
		duration: 1000,
		// 是否在图表中显示数据标签内容值
		dataLabel: true,
		// 是否在图表中显示数据点图形标识
		dataPointShape: true,
		// 数据点图形标识类型 可选值：实心solid、空心hollow
		dataPointShapeType: 'solid'
	};
	export default {
		data() {
			return {
				cWidth: 0,
				cHeight: 0,
			}
		},
		props: {
			// 图表类型
			type: {
				type: String,
				default: 'line'
			},
			// 图表配置项
			settings: {
				type: Object,
				default () {
					return {};
				},
			},
			// 宽度 需带上单位 100% 320px 320rpx
			width: {
				type: String,
				default: "100%",
			},
			// 高度 需带上单位 100% 320px 320rpx
			height: {
				type: String,
				default: "320px",
			},
			pixelRatio: {
				type: Number,
				default: 1,
			},
		},
		mounted() {
			this.formatWH(this.init);
		},
		computed: {
			canvasId() {
				return this.$hc.Command.GetGuid();
			},
			config() {
				return {
					// canvas宽度，单位为px，支付宝高分屏需要乘像素比
					width: this.cWidth * this.pixelRatio,
					// canvas高度，单位为px，支付宝高分屏需要乘像素比
					height: this.cHeight * this.pixelRatio,
					// 	图表配色方案，不传则使用系统默认配置，例如['#1890ff', '#2fc25b', '#facc14', '#f04864', '#8543e0', '#90ed7d']
					colors: this.$themeColors.map(item => item.main),
					// 页面组件canvas-id，支付宝中为id
					canvasId: this.canvasId,
					// 像素比，默认为1，仅支付宝小程序需要大于1，其他平台必须为1
					pixelRatio: this.pixelRatio,
					// 图表类型，可选值为pie、line、column、area、ring、radar、arcbar、gauge、candle、bar、mix、rose、word
					type: this.type,
				}
			}
		},
		watch: {
			width(newVal) {
				this.$nextTick(() => {
					this.formatWH();
				})
			},
			height(newVal) {
				this.$nextTick(() => {
					this.formatWH();
				})
			},
			"settings.categories": function(newVal) {
				canvases[this.canvasId].updateData({
					categories: newVal
				});
			},
			"settings.series": function(newVal) {
				canvases[this.canvasId].updateData({
					series: newVal
				});
			},
		},
		methods: {
			init() {
				const {
					settings,
					config
				} = this;
				canvases[this.canvasId] = new uCharts(Object.assign({}, baseConfig, config, settings, {
					legend: Object.assign({}, legendConfig, settings.legend),
					xAxis: Object.assign({}, xAxisConfig, settings.xAxis),
					// this实例组件内使用图表，必须传入this实例
					$this: this,
				}))
			},
			formatWH(callback) {
				const self = this;
				uni.createSelectorQuery().in(this).select('.hc-chart').boundingClientRect(res => {
					self.cWidth = res.width;
					self.cHeight = res.height;
					callback && callback();
				}).exec();
			},
			touchStart(e) {
				canvases[this.canvasId].showToolTip(e, {
					format: function(item, category) {
						return category + ' ' + item.name + ':' + item.data
					}
				});
				canvases[this.canvasId].scrollStart(e);
			},
			touchMove(e) {
				canvases[this.canvasId].scroll(e);
			},
			touchEnd(e) {
				canvases[this.canvasId].scrollEnd(e);
			},
			error(e) {
				this.$emit("error", e);
			}
		},
	};
</script>
