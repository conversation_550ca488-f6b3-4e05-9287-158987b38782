
// UUID
const UUID = "UUID";
export const GetUUID = () => uni.getStorageSync(UUID)
export const SetUUID = uuid => uni.setStorageSync(UUID, uuid)
export const RemoveUUID = () => uni.removeStorageSync(UUID)

// 用户信息
const USERINFO = "USERINFO";
export const GetUserInfo = () => uni.getStorageSync(USERINFO)
export const SetUserInfo = (userInfo) => uni.setStorageSync(USERINFO, userInfo)
export const RemoveUserInfo = () => uni.removeStorageSync(USERINFO)

// 用户名称
const USERNAME = "USERNAME";
export const GetUserName = () => uni.getStorageSync(USERNAME)
export const SetUserName = (name) => uni.setStorageSync(USERNAME, name)
export const RemoveUserName = () => uni.removeStorageSync(USERNAME)

// 用户语言信息
const USERLANG = "sflang";
export const GetUserLang = () => uni.getStorageSync(USERLANG)
export const SetUserLang = (userLang) => uni.setStorageSync(USERLANG, userLang)
export const RemoveUserLang = () => uni.removeStorageSync(USERLANG)

// 是否进入过应用
const HADENTER = "hadEnterApp";
export const GetHadEnterApp = () => uni.getStorageSync(HADENTER)
export const SetHadEnterApp = (enter) => uni.setStorageSync(HADENTER, enter)
export const RemoveHadEnterApp = () => uni.removeStorageSync(HADENTER)

// 当前主题色
const THEMEINDEX = "themeIndex";
export const GetThemeIndex = () => {
	let themeValue=uni.getStorageSync(THEMEINDEX);
	// let themeIndex = Number(themeValue);	
	
	return themeValue;
}
export const SetThemeIndex = (index) => uni.setStorageSync(THEMEINDEX, index)
export const RemoveThemeIndex = () => uni.removeStorageSync(THEMEINDEX)

const PERSONALCALENDARMODE = "PersonalCalendarMode";
export const GetPersonalCalendarMode = () => uni.getStorageSync(PERSONALCALENDARMODE)
export const SetPersonalCalendarMode = (mode) => uni.setStorageSync(PERSONALCALENDARMODE, mode)
export const RemovePersonalCalendarMode = () => uni.removeStorageSync(PERSONALCALENDARMODE)

// 服务器地址
const SERVERADDRESS = "serverAddress";
export const GetServerAddress = () => uni.getStorageSync(SERVERADDRESS)
export const SetServerAddress = (address) => uni.setStorageSync(SERVERADDRESS, address)
export const RemoveServerAddress = () => uni.removeStorageSync(SERVERADDRESS)

// 菜单权限
const MAINRIGHTS = "menuRights";
export const GetMenuRights = () => uni.getStorageSync(MAINRIGHTS)
export const SetMenuRights = (menuRights) => uni.setStorageSync(MAINRIGHTS, menuRights)
export const RemoveMenuRights = () => uni.removeStorageSync(MAINRIGHTS)

//界面资源
const sysUIResource = "sysUIResource";
export const GetSysUIResource = () => uni.getStorageSync(sysUIResource)
export const SetSysUIResource = (resource) => uni.setStorageSync(sysUIResource, resource)
export const RemoveSysUIResource = () => uni.removeStorageSync(sysUIResource)
