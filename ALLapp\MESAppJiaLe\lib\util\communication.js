﻿/**
 * 自定义事件 订阅
 * @description 用于子向父 以及 同级 页面通信 （订阅阶段）
 * @param {String} uid 程序代号
 * @param {String} name 事件名
 * @param {Function} callback 事件被emit后的回调
 */
function On(uid, name, callback) {
	uni.$on(`${uid}${name}`, callback);
};

/**
 * 自定义事件 发布
 * @description 用于子向父 以及 同级 页面通信 （发布阶段）
 * @param {String} uid 程序代号
 * @param {String} name 事件名
 * @param {Any} param 传给订阅事件回调的参数
 */
function Emit(uid, name, param) {
	uni.$emit(`${uid}${name}`, param);
};

/**
 * 自定义事件 解绑
 * @description 移除全局自定义事件监听器
 * @param {String} uid 程序代号
 * @param {String} name 事件名
 * @param {Function} callback  On订阅自定义事件时的回调函数（引用）
 */
function Off(uid, name, callback) {
	uni.$off(`${uid}${name}`, callback);
};

/**
 * 自定义事件 一次性订阅
 * @description 只触发一次，第一次触发之后移除监听器
 * @param {String} uid 程序代号
 * @param {String} name 事件名
 * @param {Function} callback  事件被emit后的回调
 */
function Once(uid, name, callback) {
	uni.$once(`${uid}${name}`, callback);
};

export default {
	On,
	Off,
	Emit,
	Once
}
