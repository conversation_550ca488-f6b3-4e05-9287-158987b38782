﻿<template>
	<view class="item" v-if="item" :key="index" @tap="Tap">
		<view class="item-left">
			<hc-image :src="$hc.Command.GetAttachmentURL(item.FATTACHMENT_ID)"></hc-image>
		</view>
		<view style="flex: 1;">
			<hc-row>
					<hc-col :span="7">
						<view class="item-content">
							<view class="item-content-title pro-desc">{{ item.FPRODUCT_NAME||"" }}</view>
							<view class="item-content-type pro-desc">品号:{{ item.FPRODUCT_CODE||"" }}</view>
							<view class="item-content-type pro-desc">规格:{{ item.FPRODUCT_DESC||"" }}</view>
						</view>
					</hc-col>
					<hc-col :span="5">
						<view class="item-right">
								￥{{ item.FPRICE||item.FSTD_SALE_PRICE||"0" }}
						</view>
					</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
	/*
	 * @example <hc-product item="{}" index="0"></hc-product>
	 */
	export default {
		name: "hc-product",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {

			};
		},
		methods:{
			Tap(){
				this.$emit("click",this.item)
			}
		}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 160rpx;
			height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}
	}

	.pro-desc {
		width: 360rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
