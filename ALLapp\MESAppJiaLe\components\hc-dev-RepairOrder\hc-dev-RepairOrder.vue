<template>
	<view class="item" v-if="item" :key="index" @tap="Tap">
		<!-- <hc-read-more :toggle="read" style="width: 100%;text-indent: 0;" show-height="300" > -->
		<view style="flex: 1;">
			<hc-row>
				<hc-col :span="12">
					<view class="item-content">
						<!-- <hc-row :index="i" v-for="(row, i) in modelList" :key="row.key">
								<hc-col :span="4" class="item-content-title pro-desc">
									<label >
										<text>{{row.Name}}：</text>
									</label>
									
								</hc-col>	
								<hc-col :span="8">
									<label >
										<text>{{item[row.Key]}}</text>
									</label>
								</hc-col>
							</hc-row> -->
						<hc-row class="key">
							<hc-col :span="2" class="item-content-title pro-desc">
								<label>
									<text>{{model.FREPAIR_TASK_CODE}}：</text>
								</label>

							</hc-col>
							<hc-col :span="10">
								<label>
									<text>{{item.FREPAIR_TASK_CODE}}</text>
								</label>
							</hc-col>

						</hc-row>

						<hc-row class="key">
							<hc-col :span="2" class="item-content-title pro-desc">
								<label>
									<text>{{model.FWORK_TYPE}}：</text>
								</label>
							</hc-col>
							<hc-col :span="10">
								<label>
									<text>{{item.FWORK_TYPE}}</text>
								</label>
							</hc-col>
						</hc-row>

						<hc-row>
							<hc-col :span="2" class="item-content-title pro-desc">
								<label>
									<text>{{model.FREPAIR_DESC}}：</text>
								</label>
							</hc-col>
							<hc-col :span="10">
								<label>
									<text>{{item.FREPAIR_DESC}}</text>
								</label>
							</hc-col>
						</hc-row>

						<hc-row>
							<hc-col :span="2" class="item-content-title pro-desc">
								<label>
									<text>{{model.FREPAIR_REASON}}：</text>
								</label>
							</hc-col>
							<hc-col :span="10">
								<label>
									<text>{{item.FREPAIR_REASON}}</text>
								</label>
							</hc-col>
						</hc-row>

						<hc-row>
							<hc-col :span="2" class="item-content-title pro-desc">
								<label>
									<text>{{model.FACTUAL_START_DATE}}：</text>
								</label>
							</hc-col>
							<hc-col :span="10">
								<label>
									<!-- <text>{{item.FACTUAL_START_DATE.split(" ")[0]}}</text> -->
									<text>{{item.FACTUAL_START_DATE?item.FACTUAL_START_DATE.split(" ")[0]:''}}</text>
								</label>
							</hc-col>
						</hc-row>
					</view>
				</hc-col>
			</hc-row>
		</view>
		<!-- </hc-read-more> -->
	</view>
</template>

<script>
	export default {
		name: "hc-dev-RepairOrder",
		props: {
			read: {
				type: Boolean,
				default: true
			},
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				modelList: [{
						Name: "单号",
						Key: "FREPAIR_TASK_CODE",
					},
					{
						Name: "类别",
						Key: "FWORK_TYPE",
					},
					{
						Name: "详情",
						Key: "FREPAIR_DESC",
					},
					{
						Name: "原因",
						Key: "FREPAIR_REASON",
					},
					{
						Name: "时间", //实际开始时间
						Key: "FACTUAL_START_DATE",
					},
				],
				model: {
					FREPAIR_TASK_CODE: "单号",
					FWORK_TYPE: "类别",
					FREPAIR_DESC: "详情",
					FREPAIR_REASON: "原因",
					FACTUAL_START_DATE: "时间", //实际开始时间
				}
			};
		},
		methods: {
			Tap() {
				this.$emit("click", this.item)
			},
		}
	}
</script>
<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;

		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
				text-align: right;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}

		.item-content {
			text-indent: 0;
		}
	}

	.Focus {
		color: red;
		font-weight: 900;
	}

	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.key {
		font-weight: bold;
	}
</style>
