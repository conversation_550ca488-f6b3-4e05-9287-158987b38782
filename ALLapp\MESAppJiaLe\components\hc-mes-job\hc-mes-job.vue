<template>
	<view class="item" v-if="item" :key="index" @tap="Tap">
		<view class="item-left">
			<hc-image  mode="aspectFit"  :src="$hc.Command.GetAttachmentURL(item.FPIC_ATTACH_ID)"></hc-image>
		</view>
		<view style="flex: 1;">
			<hc-row>
				<hc-col :span="12">
					<view class="item-content">
						<hc-row>
							<hc-col :span="12" class="item-content-title pro-desc">任务编号&nbsp:&nbsp&nbsp{{item.FCRAFT_SCHEDULE_NO}}</hc-col>							
						</hc-row>
						<hc-row>
							<hc-col :span="12" class="item-content-title pro-desc">加工产品&nbsp:&nbsp&nbsp{{item.FMATERIAL_CODE+"/"+item.FMATERIAL_NAME}} &nbsp {{item.FPLAN_QTY}}{{item.FPRO_UNIT_NAME}} </hc-col>
						</hc-row>
						<hc-row>
							<hc-col :span="12" class="item-content-title pro-desc">工位/工艺:&nbsp&nbsp{{item.FSTATION_NAME+"/"+item.FCRAFT_NAME}}</hc-col>						
						</hc-row>
						<hc-row>
							<hc-col :span="12" class="item-content-title pro-desc">计划开工&nbsp:&nbsp&nbsp{{this.$hc.Command.FormatDateTime(item.FPLAN_ST_DATE)}}</hc-col>
						</hc-row>
						<hc-row>
							<hc-col :span="12" class="item-content-title pro-desc">计划完工&nbsp:&nbsp&nbsp{{this.$hc.Command.FormatDateTime(item.FPLAN_ED_DATE)}}</hc-col>							
						</hc-row>
					</view>
				
				</hc-col>
			</hc-row>
		</view>
	
	</view>
</template>

<script>
	/*
	 * @example <hc-product item="{}" index="0"></hc-product>
	 */
	export default {
		name: "hc-mes-job",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
		
			};
		},
		methods: {
			Tap() {
				this.$emit("click", this.item)
			}
		}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-top: 1rpx solid #EEF1F5;

		&-left {
			margin-right: 20rpx;
			width: 200rpx;
			height: 240rpx;
			
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}

		&-right {
			padding-right: 10rpx;
			text-align: right;
		}
	}

	.pro-desc {
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}
</style>
