!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):n.dayjs_plugin_localeData=t()}(this,function(){"use strict";return function(n,t,e){var r=function(n){return n&&(n.indexOf?n:n.s)},o=function(n,t,e,o){var u=n.name?n:n.$locale(),a=r(u[t]),s=r(u[e]);return a||s.map(function(n){return n.substr(0,o)})},u=function(){return e.Ls[e.locale()]};t.prototype.localeData=function(){return function(){var n=this;return{months:function(t){return t?t.format("MMMM"):o(n,"months")},monthsShort:function(t){return t?t.format("MMM"):o(n,"monthsShort","months",3)},firstDayOfWeek:function(){return n.$locale().weekStart||0},weekdaysMin:function(t){return t?t.format("dd"):o(n,"weekdaysMin","weekdays",2)},weekdaysShort:function(t){return t?t.format("ddd"):o(n,"weekdaysShort","weekdays",3)},longDateFormat:function(t){return n.$locale().formats[t]}}}.bind(this)()},e.localeData=function(){var n=u();return{firstDayOfWeek:function(){return n.weekStart||0},weekdays:function(){return e.weekdays()},weekdaysShort:function(){return e.weekdaysShort()},weekdaysMin:function(){return e.weekdaysMin()},months:function(){return e.months()},monthsShort:function(){return e.monthsShort()}}},e.months=function(){return o(u(),"months")},e.monthsShort=function(){return o(u(),"monthsShort","months",3)},e.weekdays=function(){return u().weekdays},e.weekdaysShort=function(){return o(u(),"weekdaysShort","weekdays",3)},e.weekdaysMin=function(){return o(u(),"weekdaysMin","weekdays",2)}}});
