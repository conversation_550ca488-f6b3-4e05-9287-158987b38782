<template>
	<view class="test-container">
		<hc-layout>
			<hc-navbar title="标签登记优化测试" />
			
			<view class="test-section">
				<text class="section-title">优化功能测试</text>
				
				<view class="feature-list">
					<view class="feature-item">
						<text class="feature-title">✅ 1. 序号字段</text>
						<text class="feature-desc">每个标签都有自增的序号，使用系统主题色，尺寸更小</text>
					</view>

					<view class="feature-item">
						<text class="feature-title">✅ 2. 收货数量验证</text>
						<text class="feature-desc">收货数量不可以为0，会显示错误提示</text>
					</view>

					<view class="feature-item">
						<text class="feature-title">✅ 3. 前导0处理</text>
						<text class="feature-desc">输入完成后自动去除最前面的0</text>
					</view>

					<view class="feature-item">
						<text class="feature-title">✅ 4. 物料信息换行</text>
						<text class="feature-desc">物料名称、编号、单位支持换行显示，避免内容被截断</text>
					</view>
				</view>
			</view>
			
			<!-- 使用优化后的组件 -->
			<hc-out-order-card 
				:edit-data="editData"
				:index="0"
			/>
		</hc-layout>
	</view>
</template>

<script>
import HcOutOrderCard from './hc-out-order-card.vue'

export default {
	name: "optimization-test",
	
	components: {
		HcOutOrderCard
	},
	
	data() {
		return {
			editData: {
				title: "出库数",
				field: "FNUM",
				toptitle: "委外出库测试",
				billtype: "OutOrderOutScan",
				searchtitle: "委外单号"
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.test-container {
	height: 100vh;
}

.test-section {
	padding: 20rpx;
	background-color: #f8f9fa;
	margin: 20rpx;
	border-radius: 10rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.feature-list {
	.feature-item {
		margin-bottom: 20rpx;
		padding: 15rpx;
		background-color: #ffffff;
		border-radius: 8rpx;
		border-left: 4rpx solid #007aff;
		
		.feature-title {
			font-size: 28rpx;
			font-weight: bold;
			color: #007aff;
			display: block;
			margin-bottom: 8rpx;
		}
		
		.feature-desc {
			font-size: 24rpx;
			color: #666;
			line-height: 1.4;
		}
	}
}
</style>
