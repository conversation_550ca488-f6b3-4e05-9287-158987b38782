﻿/**
 * 创建数据库
 * @param {Object} options={dbname:dbname,path:path,success:success,fail:fail}
 */
function openDb(options)
{
	if (!options.path)
	{
		options.path = getPath(options.dbname);
	}
	plus.sqlite.openDatabase({
		name:options.dbname,
		path:options.path,
		//打开数据库成功
		success:function(e)
		{
			if(options.success)
			{
				options.success(e);
			}
		},
		fail:function(e)
		{
			if(options.fail)
			{
				options.fail(e);
			}
		}
		
	})
}
/**
 * @param {Object} options={dbname:dbname,success:success,fail:fail}
 */
//
function closeDb(options)
{
	
	plus.sqlite.closeDatabase({
		name:options.dbname,
		success:function(e)
		{
			if(options.success)
			{
				options.success(e);
			}
		},
		fail:function(e)
		{
			if(options.fail)
			{
				options.fail(e);
			}
		}
	})
	
}
/**
 * 删除数据库
 * begin/commit/rollback 
 * @param {Object} ={dbname:dbname,operation:operation,success:success,fail:fail}
 */
function transactionDb(options)
{
	plus.sqlite.transaction(
	{
		name:options.dbname,
		operation:options.operation,
		success:function(e)
		{
			if (options.success)
			{
				options.success(e);
			}
		},
		fail:function(e)
		{
			if (options.fail)
			{
				options.fail(e);
			}
		}
	})
}
//options={dbname:dbname,path:path}
function isOpen(options)
{
	if (!options.path)
	{
		options.path = getPath(options.dbname);
	}
	return plus.sqlite.isOpenDatabase(
	{
	                     
		path:options.path
	})
}
//options={dbname:dbname,sql:sql,success:success,fail:fail}
function executeSql(options)
{
	plus.sqlite.executeSql(
	{
		name:options.dbname,
		sql:options.sql,
		success:function(e)
		{
			if (options.success)
			{
				options.success(e);
			}
			
		},
		fail:function(e)
		{
			if (options.fail)
			{
				options.fail(e);
			}
		}
		
	}
	)
}
/**
 * 建表（需要写数据类型）
 * value= [{ffieldname:"f1",ffieldtype:"navarchar(100)"},{ffieldname:"f2",ffieldtype:"navarchar(100)"}];
 * @param {Object} options={dbname:dbname,tblname:tblname,columns:[{ffieldname:"dsfdsfdf",ffieldtype:"sdfdsfdfs"}],success:success,fail:fail}
 */
function createTable(options)
{
	var colsql="";
	
	for(let i=0;i<options.columns.length;i++)
	{
		colsql+=options.columns[i].ffieldname+" "+options.columns[i].ffieldtype+"," ;
	}
	if (colsql.length>0)
	{
	   colsql=colsql.substring(0,colsql.length-1);
	}
	let sql="create table if not exists "+options.tblname+" ("+colsql+") ";
	//executeSql(dbname,sql,success,fail);
	executeSql({dbname:options.dbname,sql:sql,success:options.success,fail:options.fail});
}

/**
 * 直接根据数据建立表（不需要写数据类型）
 * values= [{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}]
 * @param {Object} options={dbname:dbname,tblname:tblname,values:[{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}],success:success,fail:fail}
 */
function createTablebyData(options)
{
	let columns=[];
	if (options.values.length<=0) return;
	let keys=Object.keys(options.values[0]);
	let fields="";
	for (let i=0;i<keys.length;i++)
	{
		//暂定全nvarchar(1000)
		columns.push({ffieldname:keys[i],ffieldtype:"nvarchar(1000)"})
	}
	createTable({dbname:options.dbname,tblname:options.tblname,columns:columns,success:options.success,fail:options.fail});
	insertData({dbname:options.dbname,tblname:options.tblname,values:options.values,success:options.success,fail:options.fail});
	
}
//options={dbname:dbname,tblname:tblname,success:success,fail:fail}
function dropTable(options)
{
	
	let sql="drop table  "+options.tblname;
	executeSql({dbname:options.dbname,sql:sql,success:options.success,fail:options.fail});
}


/**
 * 插入数据
 * value= [{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}]
 * @param {Object} options={dbname:dbname,tblname:tblname,values:[{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}],success:success,fail:fail}
 */
function insertData(options)
{
	
	let sql=[];
	if (options.values.length<=0) return;
	let keys=Object.keys(options.values[0]);
	let insertfields="";
	for (let i=0;i<keys.length;i++)
	{
		insertfields+=keys[i]+",";
	}
	if (insertfields.length==0)
	{
		return;
	}
	insertfields=insertfields.substring(0,insertfields.length-1);
	let insertvalues="";
	for(let i=0;i<options.values.length;i++)
	{
		insertvalues="";
		for(let j=0;j<keys.length;j++)
		{
			insertvalues+="'"+options.values[i][keys[j]]+"',"
		}
		
		insertvalues=insertvalues.substring(0,insertvalues.length-1);
		sql.push("insert into "+options.tblname+"("+insertfields+") values( "+insertvalues+"); ");
		
	}
	//executeSql(dbname,sql,success,fail);
	executeSql({dbname:options.dbname,sql:sql,success:options.success,fail:options.fail});
}

/**
 * 删除表中的一条记录
 * values= [{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}],keyfielda="f1,f2"
 * @param {Object} options={dbname:dbname,tblname:tblname,values:[{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}],keyfields:keyfields,success:success,fail:fail}
 */
function deleteData(options)
{ 
	let sql=[];
	if (options.values.length<=0) return;
	let keys=Object.keys(options.values[0]);
	let pks=options.keyfields.split(",");
	
	let deletecondition="";
	for(let i=0;i<options.values.length;i++)
	{
		deletecondition="";
		for(let j=0;j<keys.length;j++)
		{
			for (let k=0;k<pks.length;k++)
			{
				if (keys[j]===pks[k])
				{
					deletecondition+=pks[k]+"='"+options.values[i][keys[j]]+"' and "
				}
			}
		}
		
		deletecondition=deletecondition.substring(0,deletecondition.length-4);
		sql.push(" delete from "+options.tblname+" where "+deletecondition + ";");
		
	}
	//executeSql(dbname,sql,success,fail);
	executeSql({dbname:options.dbname,sql:sql,success:options.success,fail:options.fail});
}
/**
 * 删除表的所有内容
 * value= [{ffieldname:"f1",ffieldtype:"navarchar(100)"},{ffieldname:"f2",ffieldtype:"navarchar(100)"}];
 * @param {Object} options={dbname:dbname,tblname:tblname,success:success,fail:fail}
 */
function truncateTable(options)
{
	let sql="delete from "+options.tblname;
	executeSql({dbname:options.dbname,sql:sql,success:options.success,fail:options.fail});
}

/**
 * 修改表
 * values= [{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}],keyfields="f1,f2"
 * @param {Object} options={dbname:dbname,tblname:tblname,values:[{f1:"2,f2:'sfs',f3:'sfsf'"},{f1:"2,f2:'sfs',f3:'sfsf'"}],keyfields:keyfields,success:success,fail:fail}
 */
function updateData(options)
{ 
	
	let sql=[];
	if (options.values.length<=0) return;
	let keys=Object.keys(options.values[0]);
	let pks=options.keyfields.split(",");
	
	let updatevalues="";
	let updatecondition="";
	for(let i=0;i<options.values.length;i++)
	{
		updatevalues="";
		updatecondition="";
		for(let j=0;j<keys.length;j++)
		{
			updatevalues+=keys[j]+"='"+options.values[i][keys[j]]+"',"
			for (let k=0;k<pks.length;k++)
			{
				if (keys[j]===pks[k])
				{
					updatecondition+=pks[k]+"='"+options.values[i][keys[j]]+"' and "
				}
			}
			
		}
		updatevalues=updatevalues.substring(0,updatevalues.length-1);
		updatecondition=updatecondition.substring(0,updatecondition.length-4);
		sql.push(" update "+options.tblname+" set "+updatevalues+" where "+updatecondition + ";");
		
	}
	//executeSql(dbname,sql,success,fail);
	executeSql({dbname:options.dbname,sql:sql,success:options.success,fail:options.fail});
}

/**
 * 查询
 * fields="f1,f2,f3",condition="f1='1'"
 *  * @param {Object} options={dbname:dbname,tblname:tblname,fields:fields,condition:condition,success:success,fail:fail}
 */
function selectData(options)
{
	if ((options.condition || "") === "")
	{
		options.condition=" 1=1 "
	}
	let sql="select "+options.fields+" from "+options.tblname+" where "+options.condition;
	//selectSql(dbname,sql,success,fail);
	selectSql({dbname:options.dbname,sql:sql,success:options.success,fail:options.fail});
	
}
//options={dbname:dbname,sql:sql,success:success,fail:fail}
function selectSql(options)
{
	plus.sqlite.selectSql(
	{
		name:options.dbname,
		sql:options.sql,
		success:function(e)
		{
			if (options.success)
			{
				options.success(e);
			}
			
		},
		fail:function(e)
		{
			if (options.fail)
			{
				options.fail(e);
			}
		}
		
	}
	)
}

function getPath(dbname)
{
	return '_doc/'+dbname+'.db';
}
export default{
	openDb,
	closeDb,
	transactionDb,
	isOpen,
	executeSql,
	selectSql,
	getPath,
	createTable,
	dropTable,
	insertData,
	updateData,
	deleteData,
	selectData,
	truncateTable,
	createTablebyData
}
