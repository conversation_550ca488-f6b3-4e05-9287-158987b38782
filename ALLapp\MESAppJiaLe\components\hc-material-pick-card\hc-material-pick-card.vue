<template>
	<view class="outBox">
		<hc-button size="mini" type="primary" shape="rect" class="btn" @click='Tap'>拣 货</hc-button>
		<view class="item" v-if="item" :key="index">
			<view class="item-left">
				<hc-image :src="$hc.Command.GetAttachmentURL(item.FPIC_ATTACH_ID)"></hc-image>
			</view>
			<view style="flex: 1;" class="item-content">
				<hc-row>
					<hc-col :span="12">
						<view class="item-content">
							<!-- <view class="item-content-type pro-left">物料编号:{{ item.FMATERIAL_CODE||"" }}</view>
						<view class="item-content-type pro-left">物料名称:{{ item.FMATERIAL_NAME||"" }}</view>
						<view class="item-content-type pro-left">规格:{{ item.FSPEC_DESC||"" }}</view>
						<view class="item-content-type pro-left">单位:{{ item.FPUR_UNIT_NAME||"" }}</view>
						<view class="item-content-type pro-left">订单数量:{{ item.FCAN_STOCKING_QTY||"" }}</view>
						<view class="item-content-type pro-left">入库数量:<hc-input></hc-input></view> -->
							<view class="title">
								{{ item.FSUB_MATERIAL_NAME||"" }}
							</view>
							<view class="desc">
								<view style="width: 100%;"><span
										class="desc-title">编号:</span>{{ item.FSUB_MATERIAL_CODE||"" }}</view>
								<!-- <view>规格:{{ item.FSPEC_DESC||"" }}</view> -->
							</view>
							<view class="desc">
								<view style="width: 80%;"><span
										class="desc-title">规格:</span>{{ item.FSUB_SPEC_DESC||0 }}
								</view>

							</view>

							<view class="desc">
								<view style="width: 100%;"><span
										class="desc-title">单位:</span>{{ item.FSUB_UNIT_NAME||"" }}</view>

							</view>


							<!-- <hc-row>
								<hc-col :span="7">
									<view class="item-content-type pro-left">名称:{{ item.FMATERIAL_NAME||"" }}</view>
								</hc-col>
								<hc-col :span="5">
									<view class="item-content-type pro-left">单位:{{ item.FSUB_UNIT_NAME||"" }}</view>
								</hc-col>
								
							</hc-row>
							<hc-row>
								<hc-col :span="7">
									<view class="item-content-type pro-left">规格:{{ item.FSPEC_DESC||"" }}</view>
								</hc-col>
								<hc-col :span="5">
									<view class="item-content-type pro-left">编号:{{ item.FMATERIAL_CODE||"" }}</view>
								</hc-col>
							</hc-row> -->

						</view>
					</hc-col>
				</hc-row>
			</view>
		</view>

		<view class="inputBox">
			<hc-row>
				<hc-col :span="4">
					<view class="item-content-type pro-left">总量:{{ item.FUSE_QTY||0 }}</view>
				</hc-col>
				<hc-col :span="4">
					<view class="item-content-type pro-left">待投:{{ (item.FUSE_QTY - item.FFETCH_QTY)||0 }}</view>
					<!-- 代投=总量-已投数量（FFETCH_QTY） -->
				</hc-col>
				<hc-col :span="4">
					<view class="item-content-type pro-left">已拣:{{ item.FCHECKOUT_QTY||0 }}</view>
				</hc-col>
				<!-- <hc-col :span="3">
					<hc-row>
						<hc-col :span="6">
							<view class="item-content-type pro-left">投放:</view>
						</hc-col>
						<hc-col :span="6">
							<hc-input type="number" v-model="item.FCHECKOUT_QTY" :customStyle="customStyle"></hc-input>
						</hc-col>
					</hc-row>
				</hc-col> -->
			</hc-row>
		</view>

	</view>

</template>

<script>
	/*
	 * @example <hc-material-recv-card item="{}" index="0"></hc-material-recv-card>
	 */
	export default {
		name: "hc-material-pick-card",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn, //获取全局主题颜色
					// "font-weight": "bold"
				}
			};
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			},

			Tap() {
				this.$emit("click", this.item, this.index)
			},
		}
	}
</script>

<style lang="scss">
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		padding-bottom: 0;

		// background-color: red;
		// border-radius: 16rpx;
		background-color: #ffffff;

		&-left {
			margin-right: 10rpx;
			min-width: 160rpx;
			height: 160rpx;
		}

		&-content {
			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 10rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}

			&-delivery-time {
				color: #000000;
				font-size: 24rpx;
			}
		}

		&-right {
			padding-right: 10rpx;

			&-decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
		}
	}

	.title {
		font-size: 28rpx;
		line-height: 50rpx;
		font-weight: bold;
		margin: 5rpx 0;
		color: #000000 !important;
	}

	.desc {
		display: flex;
		color: $u-tips-color;
		margin: 0;
	}

	.desc view {
		// width: 36%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.desc-title {}

	.pro-left {
		// width: 500rpx;
		font-size: 28rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap
	}

	.inputBox {
		padding-left: 12rpx;
	}

	.outBox {
		border-top: 10rpx #EEF1F5 solid;
		border-bottom: 10rpx #EEF1F5 solid;
		border-left: 15rpx #EEF1F5 solid;
		border-right: 15rpx #EEF1F5 solid;
		position: relative;

		width: 100%;
		// margin: 15rpx 12rpx;
		// display: flex;
	}

	.btn {
		position: absolute;
		top: 0;
		right: 0;
	}
</style>
