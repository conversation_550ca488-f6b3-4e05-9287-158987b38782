﻿<template>
	<view class="hc-ord-rps-year-amount" v-if="$haveAuth">
		<view class="hc-ord-rpa-year-amount-top">
			<view class="hc-ord-rpa-year-amount-title">
				{{title}}
			</view>
			<view class="hc-ord-rpa-year-amount-subtitle">
				{{unit}}
			</view>
		</view>
		<view>
			<hc-chart type="line" :settings="settings"></hc-chart>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'hc-ord-rpa-year-amount',
		props: {
			RpaNodeCode: {
				type: [Array, String],
				default: []
			},
		},
		data() {
			return {
				api: {
					apiGetRpaYearAmount: "/api/COP002RpaQuery/YearAmountAnalysisAsync",
				},
				settings: {
					padding: [0, 15, 15, 5],
					categories: [],
					series: [],
					dataLabel: false,
					enableScroll: false,
					// fontSize:8,
					// title:{
					// 	name:"近一年接单、审单金额分析",
					// },
					// subtitle: {
					// 	name: "单位:万"
					// },
					legend: {
						// lineHeight: "30",
						float: "right",
						margin: 25,
						fontSize:10,
					},
					xAxis: {
						// itemCount: 8,
						disableGrid: true,
						boundaryGap: "justify", //X轴从0开始
						calibration: true,
					},
					yAxis: {
						disableGrid: true,
					},
					extra: {
						line: {
							type: 'curve'
						}
					}
				},
				title: "",
				unit: ""
			};
		},
		created() {
			this.LoadData();
		},
		methods: {
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			},
			LoadData() {
				var _this = this;
				if (_this.RpaNodeCode == "") {
					return;
				}
				var option = {
					url: _this.api.apiGetRpaYearAmount,
					data: {
						nodeCodes: _this.RpaNodeCode,
					},
				};
				this.$hc.request(option).then(res => {
					if (res.StatusCode != 200) {
						_this.$hc.Command.ExceptionDialog({
							content: res.Message,
						});
					} else {
						var result = res.Entity;
						var categories = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]; //X轴标题
						var series = []; //数据

						var titleArray = [];
						result.forEach(item => {
							titleArray.push(item.RpaNodeName);
							var nodeData = {};
							nodeData.name = `${item.RpaNodeName}金额`;
							nodeData.data = [];
							// nodeData.lineType = "dash";
							// nodeData.dashLength = 3;
							for (var i = 1; i <= 12; i++) {
								nodeData.data.push(_this.MathFixedFun(parseFloat(item["Amt" + i]) / 10000));
							}
							series.push(nodeData);
						});

						if(series.length>0) series[0].color = "#F5AD3F";
						if(series.length>1) series[1].color = "#2CC181";

						_this.title = `近一年${titleArray.join("、")}金额分析`
						_this.unit = "单位:万";
						_this.settings.categories = categories;
						_this.settings.series = series;
					}
				});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.hc-ord-rpa-year-amount-top {
		position: absolute;
		left: 15rpx;
		top: 11rpx;
	}

	.hc-ord-rpa-year-amount-title {
		font-size: 32rpx;
		font-weight: 700;
	}

	.hc-ord-rpa-year-amount-subtitle {
		margin-top: 15rpx;
		font-size: 24rpx;
	}

	.hc-ord-rps-year-amount {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		margin: 15rpx 15rpx;
		position: relative;
	}
</style>
